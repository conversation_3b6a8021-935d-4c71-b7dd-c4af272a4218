/*
 * MATLAB Compiler: 23.2 (R2023b)
 * Date: Wed Jul  2 18:01:17 2025
 * Arguments: 
 * "-B""macro_default""-W""java:final_pso,Class_finalpso""-T""link:lib""-d""C:\\Users\\<USER>\\Desktop\\v_pso\\final_pso\\for_testing""class{Class_finalpso:C:\\Users\\<USER>\\Desktop\\v_pso\\vehicle_routing_system.m}"
 */

package final_pso;

import com.mathworks.toolbox.javabuilder.pooling.Poolable;
import java.util.List;
import java.rmi.Remote;
import java.rmi.RemoteException;

/**
 * The <code>Class_finalpsoRemote</code> class provides a Java RMI-compliant interface to 
 * MATLAB functions. The interface is compiled from the following files:
 * <pre>
 *  C:\\Users\\<USER>\\Desktop\\v_pso\\vehicle_routing_system.m
 * </pre>
 * The {@link #dispose} method <b>must</b> be called on a 
 * <code>Class_finalpsoRemote</code> instance when it is no longer needed to ensure that 
 * native resources allocated by this class are properly freed, and the server-side proxy 
 * is unexported.  (Failure to call dispose may result in server-side threads not being 
 * properly shut down, which often appears as a hang.)  
 *
 * This interface is designed to be used together with 
 * <code>com.mathworks.toolbox.javabuilder.remoting.RemoteProxy</code> to automatically 
 * generate RMI server proxy objects for instances of final_pso.Class_finalpso.
 */
public interface Class_finalpsoRemote extends Poolable
{
    /**
     * Provides the standard interface for calling the 
     * <code>vehicle_routing_system</code> MATLAB function with 4 input arguments.  
     *
     * Input arguments to standard interface methods may be passed as sub-classes of 
     * <code>com.mathworks.toolbox.javabuilder.MWArray</code>, or as arrays of any 
     * supported Java type (i.e. scalars and multidimensional arrays of any numeric, 
     * boolean, or character type, or String). Arguments passed as Java types are 
     * converted to MATLAB arrays according to default conversion rules.
     *
     * All inputs to this method must implement either Serializable (pass-by-value) or 
     * Remote (pass-by-reference) as per the RMI specification.
     *
     * Documentation as provided by the author of the MATLAB function:
     * <pre>
     * {@literal 
	 * % VEHICLE_ROUTING_SYSTEM - Main function for logistics path planning and vehicle 
     * scheduling
     * %
     * % Inputs:
     * %   input_file      - Path to input JSON file
     * %   output_file     - Path to output JSON file
     * %   grid_image_file - Path to grid-based path visualization PNG
     * %   route_image_file - Path to direct route visualization PNG
     * %
     * % Example usage:
     * %   vehicle_routing_system('input.json', 'output.json', 'grid_paths.png', 
     * 'direct_routes.png')
	 * }
     * </pre>
     *
     * @param rhs The inputs to the MATLAB function.
     *
     * @return Array of length nargout containing the function outputs. Outputs are 
     * returned as sub-classes of <code>com.mathworks.toolbox.javabuilder.MWArray</code>. 
     * Each output array should be freed by calling its <code>dispose()</code> method.
     *
     * @throws java.rmi.RemoteException An error has occurred during the function call or 
     * in communication with the server.
     */
    public Object[] vehicle_routing_system(Object... rhs) throws RemoteException;
  
    /** 
     * Frees native resources associated with the remote server object 
     * @throws java.rmi.RemoteException An error has occurred during the function call or in communication with the server.
     */
    void dispose() throws RemoteException;
}
