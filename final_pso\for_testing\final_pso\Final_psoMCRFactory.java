/*
 * MATLAB Compiler: 23.2 (R2023b)
 * Date: Wed Jul  2 18:01:17 2025
 * Arguments: 
 * "-B""macro_default""-W""java:final_pso,Class_finalpso""-T""link:lib""-d""C:\\Users\\<USER>\\Desktop\\v_pso\\final_pso\\for_testing""class{Class_finalpso:C:\\Users\\<USER>\\Desktop\\v_pso\\vehicle_routing_system.m}"
 */

package final_pso;

import com.mathworks.toolbox.javabuilder.*;
import com.mathworks.toolbox.javabuilder.internal.*;
import java.io.Serializable;
/**
 * <i>INTERNAL USE ONLY</i>
 */
public class Final_psoMCRFactory implements Serializable 
{
    /** Component's uuid */
    private static final String sComponentId = "final_pso_aaba5b9a-0424-4011-a783-0babafe9b8d5";
    
    /** Component name */
    private static final String sComponentName = "final_pso";
    
   
    /** Pointer to default component options */
    private static final MWComponentOptions sDefaultComponentOptions = 
        new MWComponentOptions(
            MWCtfExtractLocation.EXTRACT_TO_CACHE, 
            new MWCtfClassLoaderSource(Final_psoMCRFactory.class)
        );
    
    
    private Final_psoMCRFactory()
    {
        // Never called.
    }
    
    /**
     * Create a MWMCR instance with the required options.
     * @param componentOptions Options applied to the component.
     * @return A shared MCR instance
     * @throws MWException An error has occurred during the function call.
     */
    public static MWMCR newInstance(MWComponentOptions componentOptions) throws MWException
    {
        if (null == componentOptions.getCtfSource()) {
            componentOptions = new MWComponentOptions(componentOptions);
            componentOptions.setCtfSource(sDefaultComponentOptions.getCtfSource());
        }
        return MWMCR.newInstance(
            componentOptions, 
            Final_psoMCRFactory.class, 
            sComponentName, 
            sComponentId,
            new int[]{23,2,0}
        );
    }
    
    /**
     * Create a MWMCR instance with the default options
     * @return A MCR instance
     * @throws MWException An error has occurred during the function call.
     */
    public static MWMCR newInstance() throws MWException
    {
        return newInstance(sDefaultComponentOptions);
    }
}
