<deployment-project plugin="plugin.ezdeploy" plugin-version="1.0">
  <configuration file="C:\Users\<USER>\Desktop\v_pso\final_pso.prj" location="C:\Users\<USER>\Desktop\v_pso" name="final_pso" target="target.ezdeploy.library" target-name="Library Compiler">
    <param.appname>final_pso</param.appname>
    <param.icon />
    <param.icons />
    <param.version>1.0</param.version>
    <param.authnamewatermark />
    <param.email />
    <param.company />
    <param.summary />
    <param.description />
    <param.screenshot />
    <param.guid />
    <param.installpath.string>\final_pso\</param.installpath.string>
    <param.installpath.combo>option.installpath.programfiles</param.installpath.combo>
    <param.logo />
    <param.install.notes />
    <param.target.install.notes />
    <param.intermediate>${PROJECT_ROOT}\final_pso\for_testing</param.intermediate>
    <param.files.only>${PROJECT_ROOT}\final_pso\for_redistribution_files_only</param.files.only>
    <param.output>${PROJECT_ROOT}\final_pso\for_redistribution</param.output>
    <param.logdir>${PROJECT_ROOT}\final_pso</param.logdir>
    <param.enable.clean.build>false</param.enable.clean.build>
    <param.user.defined.mcr.options />
    <param.target.type>subtarget.java.package</param.target.type>
    <param.support.packages />
    <param.namespace />
    <param.classorg />
    <param.web.mcr>true</param.web.mcr>
    <param.package.mcr>false</param.package.mcr>
    <param.no.mcr>false</param.no.mcr>
    <param.web.mcr.name>MyAppInstaller_web</param.web.mcr.name>
    <param.package.mcr.name>MyAppInstaller_mcr</param.package.mcr.name>
    <param.no.mcr.name>MyAppInstaller_app</param.no.mcr.name>
    <param.windows.command.prompt>true</param.windows.command.prompt>
    <param.create.log>false</param.create.log>
    <param.log.file />
    <param.user.only.registration>false</param.user.only.registration>
    <param.assembly.net.version>option.net.version.four</param.assembly.net.version>
    <param.net.enable.remoting>false</param.net.enable.remoting>
    <param.assembly.type>false</param.assembly.type>
    <param.encryption.key.file />
    <param.net.tsa.enable>false</param.net.tsa.enable>
    <param.net.tsa.assembly />
    <param.net.tsa.interface />
    <param.net.tsa.namespace />
    <param.net.tsa.superclass>Class_finalpso</param.net.tsa.superclass>
    <param.net.tsa.metadata />
    <param.net.tsa.metadata.assembly>C:\Users\<USER>\Desktop\v_pso</param.net.tsa.metadata.assembly>
    <param.net.saved.interface />
    <param.cpp.api>option.cpp.all</param.cpp.api>
    <unset>
      <param.icon />
      <param.icons />
      <param.version />
      <param.authnamewatermark />
      <param.email />
      <param.company />
      <param.summary />
      <param.description />
      <param.screenshot />
      <param.guid />
      <param.installpath.string />
      <param.installpath.combo />
      <param.logo />
      <param.install.notes />
      <param.target.install.notes />
      <param.intermediate />
      <param.files.only />
      <param.output />
      <param.logdir />
      <param.enable.clean.build />
      <param.user.defined.mcr.options />
      <param.support.packages />
      <param.namespace />
      <param.classorg />
      <param.web.mcr />
      <param.package.mcr />
      <param.no.mcr />
      <param.web.mcr.name />
      <param.package.mcr.name />
      <param.no.mcr.name />
      <param.windows.command.prompt />
      <param.create.log />
      <param.log.file />
      <param.user.only.registration />
      <param.assembly.net.version />
      <param.net.enable.remoting />
      <param.assembly.type />
      <param.encryption.key.file />
      <param.net.tsa.enable />
      <param.net.tsa.assembly />
      <param.net.tsa.interface />
      <param.net.tsa.namespace />
      <param.net.tsa.superclass />
      <param.net.tsa.metadata />
      <param.net.tsa.metadata.assembly />
      <param.net.saved.interface />
      <param.cpp.api />
    </unset>
    <fileset.exports>
      <file>${PROJECT_ROOT}\vehicle_routing_system.m</file>
    </fileset.exports>
    <fileset.classes>
      <entity.package name="">
        <entity.class name="Class_finalpso">
          <file>${PROJECT_ROOT}\vehicle_routing_system.m</file>
        </entity.class>
      </entity.package>
    </fileset.classes>
    <fileset.resources />
    <fileset.depfun />
    <fileset.package />
    <fileset.examples />
    <fileset.documentation />
    <build-deliverables>
      <file location="${PROJECT_ROOT}\final_pso\for_testing" name="final_pso.jar" optional="false">C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\final_pso.jar</file>
      <file location="${PROJECT_ROOT}\final_pso\for_testing" name="doc" optional="false">C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc</file>
    </build-deliverables>
    <workflow />
    <matlab>
      <root>F:\Matlab</root>
      <toolboxes>
        <toolbox name="matlabcoder" />
        <toolbox name="embeddedcoder" />
        <toolbox name="gpucoder" />
        <toolbox name="fixedpoint" />
        <toolbox name="matlabhdlcoder" />
        <toolbox name="neuralnetwork" />
      </toolboxes>
      <toolbox>
        <matlabcoder>
          <enabled>true</enabled>
        </matlabcoder>
      </toolbox>
      <toolbox>
        <embeddedcoder>
          <enabled>true</enabled>
        </embeddedcoder>
      </toolbox>
      <toolbox>
        <gpucoder>
          <enabled>true</enabled>
        </gpucoder>
      </toolbox>
      <toolbox>
        <fixedpoint>
          <enabled>true</enabled>
        </fixedpoint>
      </toolbox>
      <toolbox>
        <matlabhdlcoder>
          <enabled>true</enabled>
        </matlabhdlcoder>
      </toolbox>
      <toolbox>
        <neuralnetwork>
          <enabled>true</enabled>
        </neuralnetwork>
      </toolbox>
    </matlab>
    <platform>
      <unix>false</unix>
      <mac>false</mac>
      <windows>true</windows>
      <win2k>false</win2k>
      <winxp>false</winxp>
      <vista>false</vista>
      <linux>false</linux>
      <solaris>false</solaris>
      <osver>10.0</osver>
      <os32>false</os32>
      <os64>true</os64>
      <arch>win64</arch>
      <matlab>true</matlab>
    </platform>
  </configuration>
</deployment-project>