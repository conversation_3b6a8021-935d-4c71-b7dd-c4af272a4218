import java.io.*;
import java.nio.file.*;
import java.util.*;

/**
 * Java测试程序 - 调用MATLAB车辆路径规划系统jar包
 * 
 * 使用说明：
 * 1. 确保MATLAB Runtime已安装
 * 2. 将vehicle_routing_system.jar放在classpath中
 * 3. 准备输入JSON文件
 * 4. 运行此测试程序
 */
public class VehicleRoutingTest {
    
    // MATLAB jar包相关配置
    private static final String JAR_NAME = "vehicle_routing_system.jar";
    private static final String MAIN_CLASS = "VehicleRoutingSystemClass"; // 根据实际情况调整
    
    // 测试文件路径
    private static final String INPUT_JSON = "input_large.json";
    private static final String OUTPUT_JSON = "output_result.json";
    private static final String VISUALIZATION_PNG = "routing_visualization.png";
    
    public static void main(String[] args) {
        VehicleRoutingTest test = new VehicleRoutingTest();
        
        System.out.println("=== MATLAB车辆路径规划系统Java测试 ===\n");
        
        try {
            // 1. 检查环境
            test.checkEnvironment();
            
            // 2. 准备测试数据
            test.prepareTestData();
            
            // 3. 调用MATLAB jar包
            test.callMatlabJar();
            
            // 4. 验证结果
            test.verifyResults();
            
            System.out.println("\n=== 测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查运行环境
     */
    private void checkEnvironment() throws Exception {
        System.out.println("1. 检查运行环境...");
        
        // 检查Java版本
        String javaVersion = System.getProperty("java.version");
        System.out.println("   Java版本: " + javaVersion);
        
        // 检查jar包是否存在
        File jarFile = new File(JAR_NAME);
        if (!jarFile.exists()) {
            throw new FileNotFoundException("找不到jar包: " + JAR_NAME);
        }
        System.out.println("   ✓ 找到jar包: " + jarFile.getAbsolutePath());
        
        // 检查MATLAB Runtime环境变量
        String mcrRoot = System.getenv("MCR_ROOT");
        if (mcrRoot != null) {
            System.out.println("   ✓ MATLAB Runtime路径: " + mcrRoot);
        } else {
            System.out.println("   ⚠ 未找到MCR_ROOT环境变量，请确保MATLAB Runtime已正确安装");
        }
        
        System.out.println("   环境检查完成\n");
    }
    
    /**
     * 准备测试数据
     */
    private void prepareTestData() throws Exception {
        System.out.println("2. 准备测试数据...");
        
        // 检查输入文件是否存在
        File inputFile = new File(INPUT_JSON);
        if (!inputFile.exists()) {
            // 如果不存在，创建一个示例输入文件
            createSampleInputFile();
        }
        
        System.out.println("   ✓ 输入文件准备完成: " + INPUT_JSON);
        
        // 清理之前的输出文件
        deleteFileIfExists(OUTPUT_JSON);
        deleteFileIfExists(VISUALIZATION_PNG);
        
        System.out.println("   ✓ 清理旧输出文件完成\n");
    }
    
    /**
     * 调用MATLAB jar包
     */
    private void callMatlabJar() throws Exception {
        System.out.println("3. 调用MATLAB jar包...");
        
        // 方法1: 使用ProcessBuilder调用jar包
        callUsingProcessBuilder();
        
        // 方法2: 如果有MATLAB生成的Java接口，可以直接调用
        // callUsingDirectInterface();
    }
    
    /**
     * 使用ProcessBuilder调用jar包
     */
    private void callUsingProcessBuilder() throws Exception {
        System.out.println("   使用ProcessBuilder调用...");
        
        // 构建命令
        List<String> command = new ArrayList<>();
        command.add("java");
        command.add("-jar");
        command.add(JAR_NAME);
        command.add(INPUT_JSON);
        command.add(OUTPUT_JSON);
        command.add(VISUALIZATION_PNG);
        
        System.out.println("   执行命令: " + String.join(" ", command));
        
        // 创建进程
        ProcessBuilder pb = new ProcessBuilder(command);
        pb.directory(new File(".")); // 设置工作目录
        pb.redirectErrorStream(true); // 合并错误流和输出流
        
        long startTime = System.currentTimeMillis();
        Process process = pb.start();
        
        // 读取输出
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println("   [MATLAB] " + line);
            }
        }
        
        // 等待进程完成
        int exitCode = process.waitFor();
        long endTime = System.currentTimeMillis();
        
        System.out.println("   进程退出码: " + exitCode);
        System.out.println("   执行时间: " + (endTime - startTime) / 1000.0 + " 秒");
        
        if (exitCode != 0) {
            throw new RuntimeException("MATLAB程序执行失败，退出码: " + exitCode);
        }
        
        System.out.println("   ✓ MATLAB程序执行成功\n");
    }
    
    /**
     * 验证结果
     */
    private void verifyResults() throws Exception {
        System.out.println("4. 验证结果...");
        
        // 检查输出JSON文件
        File outputFile = new File(OUTPUT_JSON);
        if (outputFile.exists()) {
            System.out.println("   ✓ 输出JSON文件已生成: " + outputFile.getAbsolutePath());
            System.out.println("   文件大小: " + outputFile.length() + " 字节");
            
            // 读取并显示部分结果
            displayJsonSummary(outputFile);
        } else {
            System.out.println("   ✗ 输出JSON文件未生成");
        }
        
        // 检查可视化图像文件
        File imageFile = new File(VISUALIZATION_PNG);
        if (imageFile.exists()) {
            System.out.println("   ✓ 可视化图像已生成: " + imageFile.getAbsolutePath());
            System.out.println("   文件大小: " + imageFile.length() + " 字节");
        } else {
            System.out.println("   ✗ 可视化图像未生成");
        }
    }
    
    /**
     * 显示JSON结果摘要
     */
    private void displayJsonSummary(File jsonFile) throws Exception {
        try {
            String content = new String(Files.readAllBytes(jsonFile.toPath()));
            System.out.println("   JSON内容预览 (前200字符):");
            System.out.println("   " + content.substring(0, Math.min(200, content.length())) + "...");
            
            // 简单统计
            int scheduleCount = countOccurrences(content, "\"vehicle_id\"");
            int warehouseCount = countOccurrences(content, "\"warehouse_id\"");
            
            System.out.println("   统计信息:");
            System.out.println("     - 车辆调度数: " + scheduleCount);
            System.out.println("     - 仓库访问数: " + warehouseCount);
            
        } catch (Exception e) {
            System.out.println("   读取JSON文件时出错: " + e.getMessage());
        }
    }
    
    /**
     * 创建示例输入文件
     */
    private void createSampleInputFile() throws Exception {
        String sampleJson = "{\n" +
            "  \"grid_size\": 10,\n" +
            "  \"bases\": [\n" +
            "    {\"id\": 1, \"x\": 2, \"y\": 2},\n" +
            "    {\"id\": 2, \"x\": 8, \"y\": 8}\n" +
            "  ],\n" +
            "  \"warehouses\": [\n" +
            "    {\n" +
            "      \"id\": 1,\n" +
            "      \"x\": 3,\n" +
            "      \"y\": 7,\n" +
            "      \"demands\": [{\"type\": \"A\", \"amount\": 50}],\n" +
            "      \"time_window\": [5, 15]\n" +
            "    },\n" +
            "    {\n" +
            "      \"id\": 2,\n" +
            "      \"x\": 7,\n" +
            "      \"y\": 3,\n" +
            "      \"demands\": [{\"type\": \"B\", \"amount\": 40}],\n" +
            "      \"time_window\": [8, 20]\n" +
            "    }\n" +
            "  ],\n" +
            "  \"vehicles\": [\n" +
            "    {\n" +
            "      \"id\": 1,\n" +
            "      \"capacity\": {\"A\": 100, \"B\": 80},\n" +
            "      \"cost_per_distance\": 2.5,\n" +
            "      \"initial_base\": 1\n" +
            "    }\n" +
            "  ],\n" +
            "  \"random_seed\": 42\n" +
            "}";
        
        Files.write(Paths.get(INPUT_JSON), sampleJson.getBytes());
        System.out.println("   ✓ 创建示例输入文件: " + INPUT_JSON);
    }
    
    /**
     * 辅助方法：删除文件（如果存在）
     */
    private void deleteFileIfExists(String filename) {
        File file = new File(filename);
        if (file.exists()) {
            file.delete();
        }
    }
    
    /**
     * 辅助方法：计算字符串出现次数
     */
    private int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }
}
