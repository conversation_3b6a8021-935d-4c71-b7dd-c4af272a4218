% TEST_SIMPLE_LINES - Test simplified direct route visualization
%
% This script tests the new simplified line visualization (no arrows)

clear all;
close all;
clc;

fprintf('=== 简化直线可视化测试 ===\n\n');

% Define file paths
input_file = 'input_large.json';
output_file = 'output_simple_test.json';
grid_image_file = 'grid_paths_simple.png';
route_image_file = 'direct_routes_simple.png';

% Check if input file exists
if ~exist(input_file, 'file')
    error('Input file not found: %s', input_file);
end

try
    % Run the vehicle routing system with simplified visualization
    fprintf('启动车辆路径规划系统（简化直线模式）...\n');
    tic;
    
    vehicle_routing_system(input_file, output_file, grid_image_file, route_image_file);
    
    elapsed_time = toc;
    fprintf('\n简化直线可视化测试完成!\n');
    fprintf('总执行时间: %.2f 秒\n', elapsed_time);
    
    % Verify output files
    fprintf('\n=== 输出文件验证 ===\n');
    
    if exist(route_image_file, 'file')
        fprintf('✓ 简化直线路线图已创建: %s\n', route_image_file);
        file_info = dir(route_image_file);
        fprintf('  文件大小: %.2f KB\n', file_info.bytes / 1024);
        fprintf('  现在使用简洁的直线代替箭头\n');
    else
        fprintf('✗ 简化直线路线图未创建\n');
    end
    
    if exist(grid_image_file, 'file')
        fprintf('✓ 网格路径图已创建: %s\n', grid_image_file);
        file_info = dir(grid_image_file);
        fprintf('  文件大小: %.2f KB\n', file_info.bytes / 1024);
    else
        fprintf('✗ 网格路径图未创建\n');
    end
    
    % Display improvement description
    fprintf('\n=== 可视化改进 ===\n');
    fprintf('✓ 移除了粗大的箭头\n');
    fprintf('✓ 使用简洁的直线连接\n');
    fprintf('✓ 保持4像素线宽确保可见性\n');
    fprintf('✓ 保留颜色编码和标签信息\n');
    fprintf('✓ 整体视觉效果更加清爽\n\n');
    
    fprintf('=== 测试完成 ===\n');
    
catch ME
    fprintf('测试过程中发生错误: %s\n', ME.message);
    fprintf('错误堆栈:\n');
    for i = 1:length(ME.stack)
        fprintf('  %s (第%d行)\n', ME.stack(i).name, ME.stack(i).line);
    end
end
