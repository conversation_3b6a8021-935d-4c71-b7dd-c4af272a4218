{"grid_size": 10, "bases": [{"id": 1, "x": 2, "y": 2}, {"id": 2, "x": 8, "y": 8}], "warehouses": [{"id": 1, "x": 3, "y": 7, "demands": [{"type": "A", "amount": 50}, {"type": "B", "amount": 30}], "time_window": [5, 15]}, {"id": 2, "x": 7, "y": 3, "demands": [{"type": "A", "amount": 40}, {"type": "C", "amount": 25}], "time_window": [8, 20]}, {"id": 3, "x": 5, "y": 9, "demands": [{"type": "B", "amount": 60}, {"type": "C", "amount": 35}], "time_window": [10, 25]}], "vehicles": [{"id": 1, "capacity": {"A": 100, "B": 80, "C": 60}, "cost_per_distance": 2.5, "initial_base": 1}, {"id": 2, "capacity": {"A": 80, "B": 100, "C": 70}, "cost_per_distance": 3.0, "initial_base": 2}, {"id": 3, "capacity": {"A": 60, "B": 60, "C": 100}, "cost_per_distance": 2.0, "initial_base": 1}], "random_seed": 42}