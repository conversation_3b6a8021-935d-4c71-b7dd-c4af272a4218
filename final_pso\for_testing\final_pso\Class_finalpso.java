/*
 * MATLAB Compiler: 23.2 (R2023b)
 * Date: Wed Jul  2 18:01:17 2025
 * Arguments: 
 * "-B""macro_default""-W""java:final_pso,Class_finalpso""-T""link:lib""-d""C:\\Users\\<USER>\\Desktop\\v_pso\\final_pso\\for_testing""class{Class_finalpso:C:\\Users\\<USER>\\Desktop\\v_pso\\vehicle_routing_system.m}"
 */

package final_pso;

import com.mathworks.toolbox.javabuilder.*;
import com.mathworks.toolbox.javabuilder.internal.*;
import java.util.*;
import java.io.Serializable;

/**
 * The <code>Class_finalpso</code> class provides a Java interface to MATLAB functions. 
 * The interface is compiled from the following files:
 * <pre>
 *  C:\\Users\\<USER>\\Desktop\\v_pso\\vehicle_routing_system.m
 * </pre>
 * The {@link #dispose} method <b>must</b> be called on a <code>Class_finalpso</code> 
 * instance when it is no longer needed to ensure that native resources allocated by this 
 * class are properly freed.
 * @version 0.0
 */
public class Class_finalpso extends MWComponentInstance<Class_finalpso> implements Serializable
{
    /**
     * Tracks all instances of this class to ensure their dispose method is
     * called on shutdown.
     */
    private static final Set<Disposable> sInstances = new HashSet<Disposable>();

    /**
     * Maintains information used in calling the <code>vehicle_routing_system</code> 
     *MATLAB function.
     */
    private static final MWFunctionSignature sVehicle_routing_systemSignature =
        new MWFunctionSignature(/* max outputs = */ 0,
                                /* has varargout = */ false,
                                /* function name = */ "vehicle_routing_system",
                                /* max inputs = */ 4,
                                /* has varargin = */ false);

    /**
     * Shared initialization implementation - private
     * @throws MWException An error has occurred during the function call.
     */
    private Class_finalpso (final MWMCR mcr) throws MWException
    {
        super(mcr);
        // add this to sInstances
        synchronized(Class_finalpso.class) {
            sInstances.add(this);
        }
    }

    /**
     * Constructs a new instance of the <code>Class_finalpso</code> class.
     * @throws MWException An error has occurred during the function call.
     */
    public Class_finalpso() throws MWException
    {
        this(Final_psoMCRFactory.newInstance());
    }
    
    private static MWComponentOptions getPathToComponentOptions(String path)
    {
        MWComponentOptions options = new MWComponentOptions(new MWCtfExtractLocation(path),
                                                            new MWCtfDirectorySource(path));
        return options;
    }
    
    /**
     * @deprecated Please use the constructor {@link #Class_finalpso(MWComponentOptions componentOptions)}.
     * The <code>com.mathworks.toolbox.javabuilder.MWComponentOptions</code> class provides an API to set the
     * path to the component.
     * @param pathToComponent Path to component directory.
     * @throws MWException An error has occurred during the function call.
     */
    @Deprecated
    public Class_finalpso(String pathToComponent) throws MWException
    {
        this(Final_psoMCRFactory.newInstance(getPathToComponentOptions(pathToComponent)));
    }
    
    /**
     * Constructs a new instance of the <code>Class_finalpso</code> class. Use this 
     * constructor to specify the options required to instantiate this component.  The 
     * options will be specific to the instance of this component being created.
     * @param componentOptions Options specific to the component.
     * @throws MWException An error has occurred during the function call.
     */
    public Class_finalpso(MWComponentOptions componentOptions) throws MWException
    {
        this(Final_psoMCRFactory.newInstance(componentOptions));
    }
    
    /** Frees native resources associated with this object */
    public void dispose()
    {
        try {
            super.dispose();
        } finally {
            synchronized(Class_finalpso.class) {
                sInstances.remove(this);
            }
        }
    }
    
    /**
     * Calls dispose method for each outstanding instance of this class.
     */
    public static void disposeAllInstances()
    {
        synchronized(Class_finalpso.class) {
            for (Disposable i : sInstances) i.dispose();
            sInstances.clear();
        }
    }

    /**
     * Provides the interface for calling the <code>vehicle_routing_system</code> MATLAB function 
     * where the first argument, an instance of List, receives the output of the MATLAB function and
     * the second argument, also an instance of List, provides the input to the MATLAB function.
     * <p>
     * Description as provided by the author of the MATLAB function:
     * </p>
     * <pre>
     * {@literal
	 * % VEHICLE_ROUTING_SYSTEM - Main function for logistics path planning and vehicle 
     * scheduling
     * %
     * % Inputs:
     * %   input_file      - Path to input JSON file
     * %   output_file     - Path to output JSON file
     * %   grid_image_file - Path to grid-based path visualization PNG
     * %   route_image_file - Path to direct route visualization PNG
     * %
     * % Example usage:
     * %   vehicle_routing_system('input.json', 'output.json', 'grid_paths.png', 
     * 'direct_routes.png')
	 * }
     * </pre>
     * @param lhs List in which to return outputs. Number of outputs (nargout) is
     * determined by allocated size of this List. Outputs are returned as
     * sub-classes of <code>com.mathworks.toolbox.javabuilder.MWArray</code>.
     * Each output array should be freed by calling its <code>dispose()</code>
     * method.
     *
     * @param rhs List containing inputs. Number of inputs (nargin) is determined
     * by the allocated size of this List. Input arguments may be passed as
     * sub-classes of <code>com.mathworks.toolbox.javabuilder.MWArray</code>, or
     * as arrays of any supported Java type. Arguments passed as Java types are
     * converted to MATLAB arrays according to default conversion rules.
     * @throws MWException An error has occurred during the function call.
     */
    public void vehicle_routing_system(List lhs, List rhs) throws MWException
    {
        fMCR.invoke(lhs, rhs, sVehicle_routing_systemSignature);
    }

    /**
     * Provides the interface for calling the <code>vehicle_routing_system</code> MATLAB function 
     * where the first argument, an Object array, receives the output of the MATLAB function and
     * the second argument, also an Object array, provides the input to the MATLAB function.
     * <p>
     * Description as provided by the author of the MATLAB function:
     * </p>
     * <pre>
     * {@literal
	 * % VEHICLE_ROUTING_SYSTEM - Main function for logistics path planning and vehicle 
     * scheduling
     * %
     * % Inputs:
     * %   input_file      - Path to input JSON file
     * %   output_file     - Path to output JSON file
     * %   grid_image_file - Path to grid-based path visualization PNG
     * %   route_image_file - Path to direct route visualization PNG
     * %
     * % Example usage:
     * %   vehicle_routing_system('input.json', 'output.json', 'grid_paths.png', 
     * 'direct_routes.png')
	 * }
	 * </pre>
     * @param lhs array in which to return outputs. Number of outputs (nargout)
     * is determined by allocated size of this array. Outputs are returned as
     * sub-classes of <code>com.mathworks.toolbox.javabuilder.MWArray</code>.
     * Each output array should be freed by calling its <code>dispose()</code>
     * method.
     *
     * @param rhs array containing inputs. Number of inputs (nargin) is
     * determined by the allocated size of this array. Input arguments may be
     * passed as sub-classes of
     * <code>com.mathworks.toolbox.javabuilder.MWArray</code>, or as arrays of
     * any supported Java type. Arguments passed as Java types are converted to
     * MATLAB arrays according to default conversion rules.
     * @throws MWException An error has occurred during the function call.
     */
    public void vehicle_routing_system(Object[] lhs, Object[] rhs) throws MWException
    {
        fMCR.invoke(Arrays.asList(lhs), Arrays.asList(rhs), sVehicle_routing_systemSignature);
    }

    /**
     * Provides the standard interface for calling the <code>vehicle_routing_system</code> MATLAB function with 
     * 4 comma-separated input arguments.
     * Input arguments may be passed as sub-classes of
     * <code>com.mathworks.toolbox.javabuilder.MWArray</code>, or as arrays of
     * any supported Java type. Arguments passed as Java types are converted to
     * MATLAB arrays according to default conversion rules.
     *
     * <p>
     * Description as provided by the author of the MATLAB function:
     * </p>
     * <pre>
     * {@literal
	 * % VEHICLE_ROUTING_SYSTEM - Main function for logistics path planning and vehicle 
     * scheduling
     * %
     * % Inputs:
     * %   input_file      - Path to input JSON file
     * %   output_file     - Path to output JSON file
     * %   grid_image_file - Path to grid-based path visualization PNG
     * %   route_image_file - Path to direct route visualization PNG
     * %
     * % Example usage:
     * %   vehicle_routing_system('input.json', 'output.json', 'grid_paths.png', 
     * 'direct_routes.png')
	 * }
     * </pre>
     * @param rhs The inputs to the MATLAB function.
     * @return Array of length nargout containing the function outputs. Outputs
     * are returned as sub-classes of
     * <code>com.mathworks.toolbox.javabuilder.MWArray</code>. Each output array
     * should be freed by calling its <code>dispose()</code> method.
     * @throws MWException An error has occurred during the function call.
     */
    public Object[] vehicle_routing_system(Object... rhs) throws MWException
    {
        Object[] lhs = new Object[0];
        fMCR.invoke(Arrays.asList(lhs), 
                    MWMCR.getRhsCompat(rhs, sVehicle_routing_systemSignature), 
                    sVehicle_routing_systemSignature);
        return lhs;
    }
}
