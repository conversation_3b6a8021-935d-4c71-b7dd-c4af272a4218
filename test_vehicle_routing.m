% TEST_VEHICLE_ROUTING - Test script for the vehicle routing system
%
% This script tests the vehicle routing system with example data

clear all;
close all;
clc;

fprintf('=== Vehicle Routing System Test ===\n\n');

% Define file paths
input_file = 'input_example.json';
output_file = 'output_result.json';
grid_image_file = 'routing_grid_paths.png';
route_image_file = 'routing_direct_routes.png';

% Check if input file exists
if ~exist(input_file, 'file')
    error('Input file not found: %s', input_file);
end

try
    % Run the vehicle routing system
    fprintf('Starting vehicle routing system test...\n');
    tic;
    
    vehicle_routing_system(input_file, output_file, grid_image_file, route_image_file);
    
    elapsed_time = toc;
    fprintf('\nTest completed successfully!\n');
    fprintf('Total execution time: %.2f seconds\n', elapsed_time);
    
    % Verify output files
    if exist(output_file, 'file')
        fprintf('✓ Output JSON file created: %s\n', output_file);
        
        % Display some results
        json_text = fileread(output_file);
        result = jsondecode(json_text);
        
        fprintf('\n=== Results Summary ===\n');
        fprintf('Total cost: %.2f\n', result.performance_metrics.total_cost);
        fprintf('Time penalty: %.2f\n', result.performance_metrics.time_penalty);
        fprintf('Distance penalty: %.2f\n', result.performance_metrics.distance_penalty);
        fprintf('Unfulfilled demand penalty: %.2f\n', result.performance_metrics.unfulfilled_demand_penalty);
        fprintf('Vehicle utilization imbalance: %.2f\n', result.performance_metrics.vehicle_utilization_imbalance);
        
        fprintf('\n=== Vehicle Schedules ===\n');
        for i = 1:length(result.schedule)
            schedule = result.schedule(i);
            fprintf('Vehicle %d: %d trips\n', schedule.vehicle_id, length(schedule.route));
            for j = 1:length(schedule.route)
                trip = schedule.route(j);
                fprintf('  Trip %d: Base %d → Warehouse %d (%.1f → %.1f)\n', ...
                    trip.trip, trip.base_id, trip.warehouse_id, ...
                    trip.departure_time, trip.arrival_time);
            end
        end
        
    else
        fprintf('✗ Output JSON file not created\n');
    end
    
    if exist(image_file, 'file')
        fprintf('✓ Visualization image created: %s\n', image_file);
    else
        fprintf('✗ Visualization image not created\n');
    end
    
catch ME
    fprintf('Error during test: %s\n', ME.message);
    fprintf('Stack trace:\n');
    for i = 1:length(ME.stack)
        fprintf('  %s (line %d)\n', ME.stack(i).name, ME.stack(i).line);
    end
end

fprintf('\n=== Test Complete ===\n');
