% TEST_DUAL_VISUALIZATION - Test script for dual visualization system
%
% This script tests the new dual visualization feature

clear all;
close all;
clc;

fprintf('=== 双图可视化系统测试 ===\n\n');

% Define file paths
input_file = 'input_large.json';
output_file = 'output_dual_test.json';
grid_image_file = 'grid_paths_test.png';
route_image_file = 'direct_routes_test.png';

% Check if input file exists
if ~exist(input_file, 'file')
    error('Input file not found: %s', input_file);
end

try
    % Run the vehicle routing system with dual visualization
    fprintf('启动车辆路径规划系统（双图模式）...\n');
    tic;
    
    vehicle_routing_system(input_file, output_file, grid_image_file, route_image_file);
    
    elapsed_time = toc;
    fprintf('\n双图可视化测试完成!\n');
    fprintf('总执行时间: %.2f 秒\n', elapsed_time);
    
    % Verify output files
    fprintf('\n=== 输出文件验证 ===\n');
    
    if exist(output_file, 'file')
        fprintf('✓ JSON输出文件已创建: %s\n', output_file);
        file_info = dir(output_file);
        fprintf('  文件大小: %.2f KB\n', file_info.bytes / 1024);
    else
        fprintf('✗ JSON输出文件未创建\n');
    end
    
    if exist(grid_image_file, 'file')
        fprintf('✓ 网格路径图已创建: %s\n', grid_image_file);
        file_info = dir(grid_image_file);
        fprintf('  文件大小: %.2f KB\n', file_info.bytes / 1024);
    else
        fprintf('✗ 网格路径图未创建\n');
    end
    
    if exist(route_image_file, 'file')
        fprintf('✓ 直接路线图已创建: %s\n', route_image_file);
        file_info = dir(route_image_file);
        fprintf('  文件大小: %.2f KB\n', file_info.bytes / 1024);
    else
        fprintf('✗ 直接路线图未创建\n');
    end
    
    % Display visualization descriptions
    fprintf('\n=== 可视化说明 ===\n');
    fprintf('1. %s:\n', grid_image_file);
    fprintf('   - 显示网格环境中的实际最短路径\n');
    fprintf('   - 考虑边权重的真实路径规划\n');
    fprintf('   - 适合分析路径优化效果\n\n');
    
    fprintf('2. %s:\n', route_image_file);
    fprintf('   - 显示车辆到仓库的直接分配关系\n');
    fprintf('   - 用直线箭头表示车辆任务\n');
    fprintf('   - 适合分析调度方案概览\n\n');
    
    fprintf('=== 测试完成 ===\n');
    
catch ME
    fprintf('测试过程中发生错误: %s\n', ME.message);
    fprintf('错误堆栈:\n');
    for i = 1:length(ME.stack)
        fprintf('  %s (第%d行)\n', ME.stack(i).name, ME.stack(i).line);
    end
end
