<!DOCTYPE html>
<html>
    <head>
        <title>Getting Started with the final_pso Java Package</title>
        <style type="text/css">
            .rtcContent { padding: 20px; }
            .S0 {padding:0 61px 2px 0; margin-bottom: 16px; border-bottom: 1px solid #cbcbcb; background-position: right 6px; background-repeat: no-repeat; background-size: 58px; color: #c45400; font: normal normal 22px/1.136 Arial,Helvetica,sans-serif; }
            .S1 { margin: 2px 10px 9px 4px; padding: 0px; line-height: 21px; min-height: 0px; white-space: pre-wrap; color: rgb(0, 0, 0); font-family: Arial, Helvetica, sans-serif; font-style: normal; font-size: 13px; font-weight: normal; text-align: left;  }
            .S1 a { width: 100%; padding: 5px 0; color: #0076a8; text-decoration: none; text-indent: 10px; }
            .S1 a:hover { color: #0C54B6; text-decoration: underline; }
            .S2 {padding-top: 5px; margin-bottom: 8px; color: #404040; font: normal bold 17px/1.35 <PERSON>l, Helvetica, sans-serif; border-bottom: 1px solid #ccc;}
            .code {padding: 5px; margin: 0 0 5px; border: 1px solid #d3d3d3; background: #f6f6f6;}
        </style>
    </head>
    <body>
        <div class = rtcContent>
            <h1  class = 'S0'><span>Getting Started with the final_pso Java Package</span></h1>
                <div  class = 'S1'><span>The Library Compiler in MATLAB&reg; Compiler SDK&trade; creates Java&reg; packages that can be integrated with applications written in Java. It also generates sample Java driver code that can be used to integrate and test the generated components. You can use this guide to set up your environment and run your sample driver application.</span></div>
                <div  class = 'S1'><span>Note: Sample Java driver code is only generated if sample MATLAB code is included during the packaging phase.  Samples can be found in the folder named "samples".</span></div>
                <div  class = 'S1'><span></span></div>
            <h2  class = 'S2'><span>Location of Installed Java Package Files</span></h2>
                <div  class = 'S1'><span><a href=".">View final_pso Files</a></span></div>
                <div  class = 'S1'><span><a href="./doc/html/index.html">View final_pso Javadoc</a></span></div>
                <div  class = 'S1'><span></span></div>
                <div  class = 'S1'><span></span></div>
            <h2  class = 'S2'><span>Prerequisites</span></h2>
                <div  class = 'S1'><span>1. <a href="https://www.mathworks.com/help/compiler/install-the-matlab-runtime.html">Install and Configure the MATLAB Runtime</a></span></div>
                <div  class = 'S1'><span> Complete this step only if you have not installed the MATLAB Runtime while installing the package. </span></div>
                <div  class = 'S1'><span>2. <a href="https://www.mathworks.com/help/compiler_sdk/java/configure-your-java-environment.html">Configure the Java Environment</a></span></div>
                <div  class = 'S1'><span></span></div>
            <h2  class = 'S2'><span>Compiling a Java Driver Application</span></h2>
                <div  class = 'S1'><span>At the system terminal, type:</span></div>
                <div  class = 'code'><pre>javac -cp final_pso.jar;%MATLAB_RUNTIME_INSTALL_LOCATION%/toolbox/javabuilder/jar/javabuilder.jar driverApplication.java</pre></div>
                <div  class = 'S1'><span>Replace %MATLAB_RUNTIME_INSTALL_LOCATION% with the installation folder for MATLAB Runtime.</span></div>
                <div  class = 'S1'><span>Note: You will need write permissions to the folder where you are compiling your driver application. If you do not have write permissions, copy the sample and generated component to a folder with write permissions, and then compile your driver application.</span></div>
                <div  class = 'S1'><span></span></div>
            <h2  class = 'S2'><span>Executing a Java Driver Application</span></h2>
                <div  class = 'S1'><span>At the system terminal, type:</span></div>
                <div  class = 'code'><pre>java -cp .;final_pso.jar;%MATLAB_RUNTIME_INSTALL_LOCATION%/toolbox/javabuilder/jar/javabuilder.jar driverApplication</pre></div>
                <div  class = 'S1'><span>Replace %MATLAB_RUNTIME_INSTALL_LOCATION% with the installation folder for MATLAB Runtime.</span></div>
                <div  class = 'S1'><span></span></div>
            <h2  class = 'S2'><span>Additional Resources</span></h2>
                <div  class = 'S1'><span><a href="https://www.mathworks.com/help/compiler_sdk/java_packages.html">Java Package Integration</a></span></div>
                <div  class = 'S1'><span><a href="https://www.mathworks.com/help/javabuilder/MWArrayAPI">Java MWArray API</a></span></div>
                <div  class = 'S1'><span></span></div>
        </div>
        <br>
    </body>
</html>
