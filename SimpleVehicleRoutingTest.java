import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * 简化版Java测试程序 - 调用MATLAB车辆路径规划jar包
 * 
 * 这个版本更简单，专注于核心功能测试
 */
public class SimpleVehicleRoutingTest {
    
    public static void main(String[] args) {
        System.out.println("=== MATLAB车辆路径规划系统简化测试 ===\n");
        
        // 配置参数
        String jarFile = "vehicle_routing_system.jar";
        String inputFile = "input_large.json";
        String outputFile = "output_java_test.json";
        String gridImageFile = "grid_paths_java_test.png";
        String routeImageFile = "direct_routes_java_test.png";
        
        try {
            // 检查jar包是否存在
            if (!new File(jarFile).exists()) {
                System.err.println("错误: 找不到jar包 " + jarFile);
                System.err.println("请确保jar包在当前目录下");
                return;
            }
            
            // 检查输入文件是否存在
            if (!new File(inputFile).exists()) {
                System.err.println("错误: 找不到输入文件 " + inputFile);
                System.err.println("请确保输入JSON文件在当前目录下");
                return;
            }
            
            System.out.println("✓ 找到jar包: " + jarFile);
            System.out.println("✓ 找到输入文件: " + inputFile);
            System.out.println();
            
            // 构建并执行命令
            String command = String.format("java -jar %s %s %s %s %s",
                jarFile, inputFile, outputFile, gridImageFile, routeImageFile);
            
            System.out.println("执行命令: " + command);
            System.out.println("开始运行MATLAB程序...\n");
            
            long startTime = System.currentTimeMillis();
            
            // 执行命令
            Process process = Runtime.getRuntime().exec(command);
            
            // 创建线程读取输出
            Thread outputThread = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(process.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        System.out.println("[MATLAB] " + line);
                    }
                } catch (IOException e) {
                    System.err.println("读取输出时出错: " + e.getMessage());
                }
            });
            
            // 创建线程读取错误输出
            Thread errorThread = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(process.getErrorStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        System.err.println("[MATLAB ERROR] " + line);
                    }
                } catch (IOException e) {
                    System.err.println("读取错误输出时出错: " + e.getMessage());
                }
            });
            
            outputThread.start();
            errorThread.start();
            
            // 等待进程完成，最多等待5分钟
            boolean finished = process.waitFor(5, TimeUnit.MINUTES);
            
            if (!finished) {
                System.err.println("程序运行超时，强制终止...");
                process.destroyForcibly();
                return;
            }
            
            long endTime = System.currentTimeMillis();
            int exitCode = process.exitValue();
            
            System.out.println("\n=== 执行结果 ===");
            System.out.println("退出码: " + exitCode);
            System.out.println("执行时间: " + (endTime - startTime) / 1000.0 + " 秒");
            
            if (exitCode == 0) {
                System.out.println("✓ 程序执行成功!");
                checkOutputFiles(outputFile, gridImageFile, routeImageFile);
            } else {
                System.err.println("✗ 程序执行失败!");
            }
            
        } catch (Exception e) {
            System.err.println("执行过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查输出文件
     */
    private static void checkOutputFiles(String outputFile, String gridImageFile, String routeImageFile) {
        System.out.println("\n=== 输出文件检查 ===");

        // 检查JSON输出文件
        File jsonFile = new File(outputFile);
        if (jsonFile.exists()) {
            System.out.println("✓ JSON输出文件已生成: " + outputFile);
            System.out.println("  文件大小: " + jsonFile.length() + " 字节");

            // 尝试读取并显示前几行
            try (BufferedReader reader = new BufferedReader(new FileReader(jsonFile))) {
                System.out.println("  内容预览:");
                for (int i = 0; i < 5; i++) {
                    String line = reader.readLine();
                    if (line == null) break;
                    System.out.println("    " + line);
                }
            } catch (IOException e) {
                System.err.println("  读取文件时出错: " + e.getMessage());
            }
        } else {
            System.err.println("✗ JSON输出文件未生成: " + outputFile);
        }

        // 检查网格路径图像文件
        File gridImgFile = new File(gridImageFile);
        if (gridImgFile.exists()) {
            System.out.println("✓ 网格路径可视化图像已生成: " + gridImageFile);
            System.out.println("  文件大小: " + gridImgFile.length() + " 字节");
        } else {
            System.err.println("✗ 网格路径可视化图像未生成: " + gridImageFile);
        }

        // 检查直接路线图像文件
        File routeImgFile = new File(routeImageFile);
        if (routeImgFile.exists()) {
            System.out.println("✓ 直接路线可视化图像已生成: " + routeImageFile);
            System.out.println("  文件大小: " + routeImgFile.length() + " 字节");
        } else {
            System.err.println("✗ 直接路线可视化图像未生成: " + routeImageFile);
        }
    }
}
