<html><meta charset="UTF-8"><pre>
<font color=blue>mcc -W 'java:final_pso,Class_finalpso' -T link:lib -d C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing class{Class_finalpso:C:\Users\<USER>\Desktop\v_pso\vehicle_routing_system.m} </font>


正在加载程序包final_pso的源文件...
正在构造 Javadoc 信息...
正在创建目标目录: "C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\"
标准 Doclet 版本 1.8.0_152
正在构建所有程序包和类的树...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\final_pso\Class_finalpso.html...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\final_pso\Class_finalpsoRemote.html...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\final_pso\Final_psoMCRFactory.html...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\final_pso\package-frame.html...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\final_pso\package-summary.html...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\final_pso\package-tree.html...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\constant-values.html...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\serialized-form.html...
正在构建所有程序包和类的索引...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\overview-tree.html...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\index-all.html...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\deprecated-list.html...
正在构建所有类的索引...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\allclasses-frame.html...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\allclasses-noframe.html...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\index.html...
正在生成C:\Users\<USER>\Desktop\v_pso\final_pso\for_testing\doc\html\help-doc.html...


<font color=blue>正在打包...</font>
Creating the bundle...
Creating the install agent URL file...
Web based installer created at C:\Users\<USER>\Desktop\v_pso\final_pso\for_redistribution\MyAppInstaller_web.exe.
Packaging complete.
Elapsed packaging time was: 5 seconds.
</pre></html>
