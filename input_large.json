{"grid_size": 10, "bases": [{"id": 1, "x": 2, "y": 2}, {"id": 2, "x": 8, "y": 8}, {"id": 3, "x": 5, "y": 1}], "warehouses": [{"id": 1, "x": 1, "y": 4, "demands": [{"type": "A", "amount": 80}, {"type": "B", "amount": 60}], "time_window": [2, 25]}, {"id": 2, "x": 3, "y": 7, "demands": [{"type": "C", "amount": 90}, {"type": "E", "amount": 45}], "time_window": [3, 28]}, {"id": 3, "x": 6, "y": 3, "demands": [{"type": "A", "amount": 120}, {"type": "D", "amount": 70}], "time_window": [4, 30]}, {"id": 4, "x": 9, "y": 6, "demands": [{"type": "B", "amount": 100}, {"type": "C", "amount": 85}], "time_window": [2, 26]}, {"id": 5, "x": 2, "y": 9, "demands": [{"type": "E", "amount": 55}], "time_window": [5, 32]}, {"id": 6, "x": 7, "y": 2, "demands": [{"type": "A", "amount": 95}, {"type": "B", "amount": 75}], "time_window": [3, 29]}, {"id": 7, "x": 4, "y": 8, "demands": [{"type": "C", "amount": 110}, {"type": "D", "amount": 65}], "time_window": [6, 35]}, {"id": 8, "x": 10, "y": 4, "demands": [{"type": "D", "amount": 130}, {"type": "E", "amount": 40}], "time_window": [3, 27]}, {"id": 9, "x": 1, "y": 10, "demands": [{"type": "A", "amount": 85}, {"type": "C", "amount": 95}], "time_window": [7, 38]}, {"id": 10, "x": 8, "y": 1, "demands": [{"type": "B", "amount": 115}, {"type": "D", "amount": 80}], "time_window": [2, 24]}, {"id": 11, "x": 3, "y": 5, "demands": [{"type": "E", "amount": 50}], "time_window": [4, 31]}, {"id": 12, "x": 9, "y": 9, "demands": [{"type": "A", "amount": 105}, {"type": "B", "amount": 90}], "time_window": [8, 40]}, {"id": 13, "x": 6, "y": 7, "demands": [{"type": "C", "amount": 125}, {"type": "D", "amount": 95}], "time_window": [4, 30]}, {"id": 14, "x": 1, "y": 6, "demands": [{"type": "B", "amount": 80}, {"type": "E", "amount": 35}], "time_window": [5, 34]}, {"id": 15, "x": 10, "y": 10, "demands": [{"type": "C", "amount": 70}, {"type": "D", "amount": 110}], "time_window": [9, 42]}], "vehicles": [{"id": 1, "capacity": {"A": 50, "B": 40, "C": 45, "D": 35, "E": 0}, "cost_per_distance": 2.5, "initial_base": 1}, {"id": 2, "capacity": {"A": 60, "B": 50, "C": 40, "D": 45, "E": 0}, "cost_per_distance": 2.8, "initial_base": 1}, {"id": 3, "capacity": {"A": 45, "B": 35, "C": 50, "D": 40, "E": 0}, "cost_per_distance": 3.0, "initial_base": 1}, {"id": 4, "capacity": {"A": 55, "B": 45, "C": 35, "D": 50, "E": 0}, "cost_per_distance": 2.2, "initial_base": 2}, {"id": 5, "capacity": {"A": 40, "B": 55, "C": 50, "D": 30, "E": 0}, "cost_per_distance": 2.7, "initial_base": 2}, {"id": 6, "capacity": {"A": 35, "B": 40, "C": 55, "D": 45, "E": 0}, "cost_per_distance": 3.2, "initial_base": 2}, {"id": 7, "capacity": {"A": 0, "B": 0, "C": 0, "D": 0, "E": 30}, "cost_per_distance": 2.4, "initial_base": 3}, {"id": 8, "capacity": {"A": 0, "B": 0, "C": 0, "D": 0, "E": 40}, "cost_per_distance": 2.9, "initial_base": 3}, {"id": 9, "capacity": {"A": 0, "B": 0, "C": 0, "D": 0, "E": 35}, "cost_per_distance": 2.6, "initial_base": 3}], "random_seed": 42}