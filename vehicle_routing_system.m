function vehicle_routing_system(input_file, output_file, grid_image_file, route_image_file)
% VEHICLE_ROUTING_SYSTEM - Main function for logistics path planning and vehicle scheduling
%
% Inputs:
%   input_file      - Path to input JSON file
%   output_file     - Path to output JSON file
%   grid_image_file - Path to grid-based path visualization PNG
%   route_image_file - Path to direct route visualization PNG
%
% Example usage:
%   vehicle_routing_system('input.json', 'output.json', 'grid_paths.png', 'direct_routes.png')

    try
        % Parse input JSON file
        fprintf('Parsing input file: %s\n', input_file);
        data = parseInput(input_file);
        
        % Generate grid graph with random edge weights
        fprintf('Generating grid graph...\n');
        [grid, edge_weights] = generateGrid(data.grid_size, data.random_seed);
        
        % Calculate shortest paths from all bases to all warehouses
        fprintf('Calculating shortest paths...\n');
        [distances, paths] = calculateAllShortestPaths(grid, edge_weights, data.bases, data.warehouses);
        
        % Optimize vehicle scheduling using Adaptive Multi-Strategy PSO algorithm
        fprintf('Optimizing vehicle scheduling with Adaptive Multi-Strategy PSO...\n');
        [best_schedule, best_fitness] = psoScheduling(data, distances, paths);
        
        % Generate detailed schedule plan
        fprintf('Generating detailed schedule...\n');
        detailed_schedule = generateDetailedSchedule(data, best_schedule, distances, paths);
        
        % Create visualizations
        fprintf('Creating visualizations...\n');
        createVisualizationsTwo(grid, data, detailed_schedule, paths, grid_image_file, route_image_file);
        
        % Write output JSON
        fprintf('Writing output file: %s\n', output_file);
        writeOutput(detailed_schedule, output_file);
        
        fprintf('Vehicle routing system completed successfully!\n');
        fprintf('Total cost: %.2f\n', detailed_schedule.performance_metrics.total_cost);
        
    catch ME
        fprintf('Error in vehicle_routing_system: %s\n', ME.message);
        rethrow(ME);
    end
end

function data = parseInput(input_file)
% PARSEINPUT - Parse JSON input file and return structured data
    
    if ~exist(input_file, 'file')
        error('Input file does not exist: %s', input_file);
    end
    
    % Read JSON file
    json_text = fileread(input_file);
    raw_data = jsondecode(json_text);
    
    % Structure the data
    data = struct();
    data.grid_size = raw_data.grid_size;
    data.random_seed = raw_data.random_seed;
    
    % Process bases
    data.bases = [];
    for i = 1:length(raw_data.bases)
        base = raw_data.bases(i);
        data.bases(i).id = base.id;
        data.bases(i).x = base.x;
        data.bases(i).y = base.y;
    end
    
    % Process warehouses
    data.warehouses = [];
    for i = 1:length(raw_data.warehouses)
        wh = raw_data.warehouses(i);
        data.warehouses(i).id = wh.id;
        data.warehouses(i).x = wh.x;
        data.warehouses(i).y = wh.y;
        data.warehouses(i).demands = wh.demands;
        data.warehouses(i).time_window = wh.time_window;
    end
    
    % Process vehicles
    data.vehicles = [];
    for i = 1:length(raw_data.vehicles)
        veh = raw_data.vehicles(i);
        data.vehicles(i).id = veh.id;
        data.vehicles(i).capacity = veh.capacity;
        data.vehicles(i).cost_per_distance = veh.cost_per_distance;
        data.vehicles(i).initial_base = veh.initial_base;
    end
    
    fprintf('Parsed data: %d bases, %d warehouses, %d vehicles\n', ...
        length(data.bases), length(data.warehouses), length(data.vehicles));
end

function [grid, edge_weights] = generateGrid(grid_size, random_seed)
% GENERATEGRID - Generate n×n grid graph with random edge weights
    
    rng(random_seed); % Set random seed for reproducibility
    
    % Initialize grid (1 = passable, 0 = blocked)
    grid = ones(grid_size, grid_size);
    
    % Generate random edge weights for 4-connected grid
    % edge_weights(i,j,direction) where direction: 1=right, 2=down, 3=left, 4=up
    edge_weights = rand(grid_size, grid_size, 4) * 10 + 1; % Random weights between 1-11
    
    % Ensure symmetry: if edge from A to B has weight w, then B to A also has weight w
    for i = 1:grid_size
        for j = 1:grid_size
            % Right edge symmetry
            if j < grid_size
                edge_weights(i, j+1, 3) = edge_weights(i, j, 1); % left = right
            end
            % Down edge symmetry  
            if i < grid_size
                edge_weights(i+1, j, 4) = edge_weights(i, j, 2); % up = down
            end
        end
    end
    
    fprintf('Generated %dx%d grid with random edge weights\n', grid_size, grid_size);
end

function [distances, paths] = calculateAllShortestPaths(grid, edge_weights, bases, warehouses)
% CALCULATEALLSHORTESTPATHS - Calculate shortest paths from all bases to all warehouses
    
    num_bases = length(bases);
    num_warehouses = length(warehouses);
    
    distances = zeros(num_bases, num_warehouses);
    paths = cell(num_bases, num_warehouses);
    
    for i = 1:num_bases
        for j = 1:num_warehouses
            [dist, path] = shortestPath(grid, edge_weights, ...
                [bases(i).x, bases(i).y], [warehouses(j).x, warehouses(j).y]);
            distances(i, j) = dist;
            paths{i, j} = path;
        end
    end
    
    fprintf('Calculated shortest paths: %dx%d distance matrix\n', num_bases, num_warehouses);
end

function [distance, path] = shortestPath(grid, edge_weights, start_pos, end_pos)
% SHORTESTPATH - Hybrid A* algorithm with edge-weight-aware strategies

    [rows, cols, ~] = size(edge_weights);

    % Convert positions to indices
    start_idx = sub2ind([rows, cols], start_pos(1), start_pos(2));
    end_idx = sub2ind([rows, cols], end_pos(1), end_pos(2));

    % Calculate Manhattan distance for initial strategy selection
    manhattan_dist = abs(start_pos(1) - end_pos(1)) + abs(start_pos(2) - end_pos(2));

    % Analyze edge weight characteristics for better strategy selection
    avg_edge_weight = mean(edge_weights(:));
    weight_variance = var(edge_weights(:));

    % Strategy selection based on distance, edge weight patterns, and search complexity
    if manhattan_dist <= 6 || (manhattan_dist <= 10 && weight_variance < 2)
        % Short distance OR low weight variance: Use enhanced A* with better heuristic
        [distance, path] = hybridAStar_EnhancedHeuristic(edge_weights, start_pos, end_pos, start_idx, end_idx, rows, cols, avg_edge_weight);
    elseif manhattan_dist <= 15 && weight_variance >= 2
        % Medium distance with high weight variance: Use bidirectional A*
        [distance, path] = hybridAStar_Bidirectional(edge_weights, start_pos, end_pos, start_idx, end_idx, rows, cols, avg_edge_weight);
    else
        % Long distance: Use hierarchical A* with weight-aware waypoint selection
        [distance, path] = hybridAStar_WeightAwareHierarchical(edge_weights, start_pos, end_pos, start_idx, end_idx, rows, cols, avg_edge_weight);
    end
end

function [distance, path] = hybridAStar_EnhancedHeuristic(edge_weights, start_pos, end_pos, start_idx, end_idx, rows, cols, avg_edge_weight)
% Enhanced A* with weight-aware heuristic for short-medium distances

    % Weight-aware heuristic: scale Manhattan distance by average edge weight
    % Add local weight sampling for better estimation
    heuristic = @(pos) calculateWeightAwareHeuristic(pos, end_pos, edge_weights, avg_edge_weight, rows, cols);

    [distance, path] = executeAStar(edge_weights, start_pos, end_pos, start_idx, end_idx, rows, cols, heuristic);
end

function h_value = calculateWeightAwareHeuristic(pos, end_pos, edge_weights, avg_edge_weight, rows, cols)
% Calculate heuristic that considers local edge weights

    % Basic Manhattan distance
    manhattan = abs(pos(1) - end_pos(1)) + abs(pos(2) - end_pos(2));

    % Sample local edge weights around current position for better estimation
    local_weight_sum = 0;
    sample_count = 0;

    % Sample weights in a small neighborhood
    for dr = -1:1
        for dc = -1:1
            r = pos(1) + dr;
            c = pos(2) + dc;
            if r >= 1 && r <= rows && c >= 1 && c <= cols
                % Sample all 4 directions from this position
                for dir = 1:4
                    if (dir == 1 && c < cols) || (dir == 2 && r < rows) || ...
                       (dir == 3 && c > 1) || (dir == 4 && r > 1)
                        local_weight_sum = local_weight_sum + edge_weights(r, c, dir);
                        sample_count = sample_count + 1;
                    end
                end
            end
        end
    end

    % Use local average if available, otherwise use global average
    if sample_count > 0
        local_avg_weight = local_weight_sum / sample_count;
        weight_factor = 0.7 * local_avg_weight + 0.3 * avg_edge_weight;
    else
        weight_factor = avg_edge_weight;
    end

    % Scale Manhattan distance by estimated edge weight
    h_value = manhattan * weight_factor;
end

function [distance, path] = hybridAStar_Bidirectional(edge_weights, start_pos, end_pos, start_idx, end_idx, rows, cols, avg_edge_weight)
% Bidirectional A* for medium distances with high weight variance

    % Initialize forward search (from start)
    g_forward = inf(rows * cols, 1);
    f_forward = inf(rows * cols, 1);
    g_forward(start_idx) = 0;
    f_forward(start_idx) = avg_edge_weight * (abs(start_pos(1) - end_pos(1)) + abs(start_pos(2) - end_pos(2)));

    open_forward = start_idx;
    closed_forward = false(rows * cols, 1);
    parent_forward = zeros(rows * cols, 1);

    % Initialize backward search (from end)
    g_backward = inf(rows * cols, 1);
    f_backward = inf(rows * cols, 1);
    g_backward(end_idx) = 0;
    f_backward(end_idx) = avg_edge_weight * (abs(end_pos(1) - start_pos(1)) + abs(end_pos(2) - start_pos(2)));

    open_backward = end_idx;
    closed_backward = false(rows * cols, 1);
    parent_backward = zeros(rows * cols, 1);

    best_path_length = inf;
    meeting_point = -1;

    % Bidirectional search loop
    while ~isempty(open_forward) && ~isempty(open_backward)
        % Expand forward search
        if ~isempty(open_forward)
            [current_forward, open_forward, closed_forward] = expandBidirectionalNode(...
                open_forward, closed_forward, g_forward, f_forward, parent_forward, ...
                edge_weights, end_pos, avg_edge_weight, rows, cols, true);

            % Check if forward search meets backward search
            if current_forward ~= -1 && closed_backward(current_forward)
                total_length = g_forward(current_forward) + g_backward(current_forward);
                if total_length < best_path_length
                    best_path_length = total_length;
                    meeting_point = current_forward;
                end
            end
        end

        % Expand backward search
        if ~isempty(open_backward)
            [current_backward, open_backward, closed_backward] = expandBidirectionalNode(...
                open_backward, closed_backward, g_backward, f_backward, parent_backward, ...
                edge_weights, start_pos, avg_edge_weight, rows, cols, false);

            % Check if backward search meets forward search
            if current_backward ~= -1 && closed_forward(current_backward)
                total_length = g_forward(current_backward) + g_backward(current_backward);
                if total_length < best_path_length
                    best_path_length = total_length;
                    meeting_point = current_backward;
                end
            end
        end

        % Early termination if we found a meeting point
        if meeting_point ~= -1
            break;
        end
    end

    if meeting_point == -1
        % Fallback to standard A* if bidirectional search fails
        heuristic = @(pos) avg_edge_weight * (abs(pos(1) - end_pos(1)) + abs(pos(2) - end_pos(2)));
        [distance, path] = executeAStar(edge_weights, start_pos, end_pos, start_idx, end_idx, rows, cols, heuristic);
    else
        % Reconstruct path from both directions
        [distance, path] = reconstructBidirectionalPath(parent_forward, parent_backward, meeting_point, ...
            start_idx, end_idx, rows, cols, best_path_length);
    end
end

function [current_node, open_set, closed_set] = expandBidirectionalNode(open_set, closed_set, g_score, f_score, parent, edge_weights, target_pos, avg_edge_weight, rows, cols, is_forward)
% Expand one node in bidirectional search

    if isempty(open_set)
        current_node = -1;
        return;
    end

    % Find node with lowest f_score
    [~, min_idx] = min(f_score(open_set));
    current_node = open_set(min_idx);

    % Remove from open set and add to closed set
    open_set(min_idx) = [];
    closed_set(current_node) = true;

    [row, col] = ind2sub([rows, cols], current_node);

    % Explore neighbors
    neighbors = [
        row, col+1, 1;  % right
        row+1, col, 2;  % down
        row, col-1, 3;  % left
        row-1, col, 4   % up
    ];

    for n = 1:4
        nr = neighbors(n, 1);
        nc = neighbors(n, 2);
        direction = neighbors(n, 3);

        if nr >= 1 && nr <= rows && nc >= 1 && nc <= cols
            neighbor_idx = sub2ind([rows, cols], nr, nc);

            if closed_set(neighbor_idx)
                continue;
            end

            % Get edge weight (consider direction for backward search)
            if is_forward
                edge_weight = edge_weights(row, col, direction);
            else
                % For backward search, use reverse direction
                reverse_dir = mod(direction + 1, 4) + 1; % 1->3, 2->4, 3->1, 4->2
                if direction == 1, reverse_dir = 3; end
                if direction == 2, reverse_dir = 4; end
                if direction == 3, reverse_dir = 1; end
                if direction == 4, reverse_dir = 2; end
                edge_weight = edge_weights(nr, nc, reverse_dir);
            end

            tentative_g = g_score(current_node) + edge_weight;

            if ~ismember(neighbor_idx, open_set)
                open_set = [open_set; neighbor_idx];
            elseif tentative_g >= g_score(neighbor_idx)
                continue;
            end

            parent(neighbor_idx) = current_node;
            g_score(neighbor_idx) = tentative_g;

            % Calculate heuristic to target
            heuristic_value = avg_edge_weight * (abs(nr - target_pos(1)) + abs(nc - target_pos(2)));
            f_score(neighbor_idx) = tentative_g + heuristic_value;
        end
    end
end

function [distance, path] = reconstructBidirectionalPath(parent_forward, parent_backward, meeting_point, start_idx, end_idx, rows, cols, total_distance)
% Reconstruct path from bidirectional search

    % Reconstruct forward path (start to meeting point)
    forward_path = [];
    current = meeting_point;
    while current ~= 0
        [r, c] = ind2sub([rows, cols], current);
        forward_path = [[r, c]; forward_path];
        current = parent_forward(current);
    end

    % Reconstruct backward path (meeting point to end)
    backward_path = [];
    current = parent_backward(meeting_point);
    while current ~= 0
        [r, c] = ind2sub([rows, cols], current);
        backward_path = [backward_path; [r, c]];
        current = parent_backward(current);
    end

    % Combine paths
    path = [forward_path; backward_path];
    distance = total_distance;
end

function [distance, path] = hybridAStar_WeightAwareHierarchical(edge_weights, start_pos, end_pos, start_idx, end_idx, rows, cols, avg_edge_weight)
% Weight-aware hierarchical A* for long distances

    % Analyze edge weight patterns to generate smarter waypoints
    waypoints = generateWeightAwareWaypoints(start_pos, end_pos, edge_weights, rows, cols, avg_edge_weight);

    % Plan path through waypoints with weight-aware heuristic
    full_path = start_pos;
    total_distance = 0;
    current_pos = start_pos;

    for i = 1:size(waypoints, 1)
        target_pos = waypoints(i, :);

        % Use weight-aware heuristic for each segment
        heuristic = @(pos) avg_edge_weight * (abs(pos(1) - target_pos(1)) + abs(pos(2) - target_pos(2)));
        current_idx = sub2ind([rows, cols], current_pos(1), current_pos(2));
        target_idx = sub2ind([rows, cols], target_pos(1), target_pos(2));

        [segment_dist, segment_path] = executeAStar(edge_weights, current_pos, target_pos, current_idx, target_idx, rows, cols, heuristic);

        if ~isinf(segment_dist) && ~isempty(segment_path)
            % Remove duplicate start point
            if size(full_path, 1) > 1
                segment_path = segment_path(2:end, :);
            end
            full_path = [full_path; segment_path];
            total_distance = total_distance + segment_dist;
            current_pos = target_pos;
        else
            % Fallback: try direct path to final destination
            fprintf('Waypoint unreachable, attempting direct path...\n');
            break;
        end
    end

    % Final segment to destination
    if ~isequal(current_pos, end_pos)
        heuristic = @(pos) avg_edge_weight * (abs(pos(1) - end_pos(1)) + abs(pos(2) - end_pos(2)));
        current_idx = sub2ind([rows, cols], current_pos(1), current_pos(2));

        [segment_dist, segment_path] = executeAStar(edge_weights, current_pos, end_pos, current_idx, end_idx, rows, cols, heuristic);

        if ~isinf(segment_dist) && ~isempty(segment_path)
            segment_path = segment_path(2:end, :);
            full_path = [full_path; segment_path];
            total_distance = total_distance + segment_dist;
        end
    end

    distance = total_distance;
    path = full_path;
end

function waypoints = generateWeightAwareWaypoints(start_pos, end_pos, edge_weights, rows, cols, avg_edge_weight)
% Generate waypoints considering edge weight patterns

    % Calculate direction vector
    dx = end_pos(1) - start_pos(1);
    dy = end_pos(2) - start_pos(2);
    total_dist = sqrt(dx^2 + dy^2);

    if total_dist <= 12
        waypoints = end_pos;
        return;
    end

    % Generate fewer waypoints for weight-aware approach
    num_waypoints = min(3, floor(total_dist / 10));
    waypoints = zeros(num_waypoints + 1, 2);

    for i = 1:num_waypoints
        ratio = i / (num_waypoints + 1);

        % Base interpolated position
        base_x = round(start_pos(1) + ratio * dx);
        base_y = round(start_pos(2) + ratio * dy);

        % Ensure base position is within bounds
        base_x = max(1, min(rows, base_x));
        base_y = max(1, min(cols, base_y));

        % Look for locally optimal positions around the base position
        best_x = base_x;
        best_y = base_y;
        best_weight_score = inf;

        % Search in a small neighborhood for better waypoint positions
        search_radius = 2;
        for dx_offset = -search_radius:search_radius
            for dy_offset = -search_radius:search_radius
                candidate_x = base_x + dx_offset;
                candidate_y = base_y + dy_offset;

                if candidate_x >= 1 && candidate_x <= rows && candidate_y >= 1 && candidate_y <= cols
                    % Calculate average weight around this candidate position
                    local_weight = calculateLocalAverageWeight(candidate_x, candidate_y, edge_weights, rows, cols);

                    % Prefer positions with lower average weights
                    if local_weight < best_weight_score
                        best_weight_score = local_weight;
                        best_x = candidate_x;
                        best_y = candidate_y;
                    end
                end
            end
        end

        waypoints(i, :) = [best_x, best_y];
    end

    waypoints(end, :) = end_pos;
end

function avg_weight = calculateLocalAverageWeight(x, y, edge_weights, rows, cols)
% Calculate average edge weight in local neighborhood

    weight_sum = 0;
    count = 0;

    % Sample weights in immediate neighborhood
    for dx = -1:1
        for dy = -1:1
            nx = x + dx;
            ny = y + dy;
            if nx >= 1 && nx <= rows && ny >= 1 && ny <= cols
                % Sample all 4 directions from this position
                for dir = 1:4
                    if (dir == 1 && ny < cols) || (dir == 2 && nx < rows) || ...
                       (dir == 3 && ny > 1) || (dir == 4 && nx > 1)
                        weight_sum = weight_sum + edge_weights(nx, ny, dir);
                        count = count + 1;
                    end
                end
            end
        end
    end

    if count > 0
        avg_weight = weight_sum / count;
    else
        avg_weight = mean(edge_weights(:)); % Fallback to global average
    end
end

function waypoints = generateWaypoints(start_pos, end_pos, rows, cols)
% GENERATEWAYPOINTS - Generate strategic waypoints for hierarchical pathfinding

    % Calculate direction vector
    dx = end_pos(1) - start_pos(1);
    dy = end_pos(2) - start_pos(2);
    total_dist = sqrt(dx^2 + dy^2);

    if total_dist <= 10
        waypoints = end_pos;
        return;
    end

    % Generate waypoints at strategic intervals
    num_waypoints = min(4, floor(total_dist / 8));
    waypoints = zeros(num_waypoints + 1, 2);

    for i = 1:num_waypoints
        ratio = i / (num_waypoints + 1);

        % Add some strategic deviation to avoid obstacles
        deviation_x = 0.2 * sin(ratio * pi) * (rand - 0.5) * min(rows, cols) * 0.1;
        deviation_y = 0.2 * cos(ratio * pi) * (rand - 0.5) * min(rows, cols) * 0.1;

        wp_x = round(start_pos(1) + ratio * dx + deviation_x);
        wp_y = round(start_pos(2) + ratio * dy + deviation_y);

        % Ensure waypoints are within bounds
        wp_x = max(1, min(rows, wp_x));
        wp_y = max(1, min(cols, wp_y));

        waypoints(i, :) = [wp_x, wp_y];
    end

    waypoints(end, :) = end_pos;
end

function [distance, path] = executeAStar(edge_weights, start_pos, end_pos, start_idx, end_idx, rows, cols, heuristic)
% EXECUTEASTAR - Core A* algorithm execution

    g_score = inf(rows * cols, 1);
    f_score = inf(rows * cols, 1);
    g_score(start_idx) = 0;
    f_score(start_idx) = heuristic(start_pos);

    open_set = start_idx;
    closed_set = false(rows * cols, 1);
    parent = zeros(rows * cols, 1);

    while ~isempty(open_set)
        [~, min_idx] = min(f_score(open_set));
        current_idx = open_set(min_idx);

        open_set(min_idx) = [];
        closed_set(current_idx) = true;

        if current_idx == end_idx
            break;
        end

        [row, col] = ind2sub([rows, cols], current_idx);

        neighbors = [
            row, col+1, 1;  % right
            row+1, col, 2;  % down
            row, col-1, 3;  % left
            row-1, col, 4   % up
        ];

        for n = 1:4
            nr = neighbors(n, 1);
            nc = neighbors(n, 2);
            direction = neighbors(n, 3);

            if nr >= 1 && nr <= rows && nc >= 1 && nc <= cols
                neighbor_idx = sub2ind([rows, cols], nr, nc);

                if closed_set(neighbor_idx)
                    continue;
                end

                edge_weight = edge_weights(row, col, direction);
                tentative_g = g_score(current_idx) + edge_weight;

                if ~ismember(neighbor_idx, open_set)
                    open_set = [open_set; neighbor_idx];
                elseif tentative_g >= g_score(neighbor_idx)
                    continue;
                end

                parent(neighbor_idx) = current_idx;
                g_score(neighbor_idx) = tentative_g;
                f_score(neighbor_idx) = tentative_g + heuristic([nr, nc]);
            end
        end
    end

    [distance, path] = reconstructPath(parent, end_idx, g_score, rows, cols, start_pos, end_pos);
end

function [distance, path] = reconstructPath(parent, end_idx, g_score, rows, cols, start_pos, end_pos)
% RECONSTRUCTPATH - Reconstruct path from parent array

    path = [];
    current = end_idx;
    while current ~= 0
        [r, c] = ind2sub([rows, cols], current);
        path = [[r, c]; path];
        current = parent(current);
    end

    distance = g_score(end_idx);
    if isinf(distance)
        warning('No path found from [%d,%d] to [%d,%d]', start_pos(1), start_pos(2), end_pos(1), end_pos(2));
        path = [];
    end
end

function [best_schedule, best_fitness] = psoScheduling(data, distances, paths)
% PSOSCHEDULING - Adaptive Multi-Strategy PSO for vehicle scheduling optimization

    num_vehicles = length(data.vehicles);
    num_warehouses = length(data.warehouses);
    max_trips = max(10, ceil(num_warehouses * 1.5 / num_vehicles));

    % AMPSO parameters
    num_particles = min(150, max(50, num_warehouses * 8));
    max_iterations = min(5000, max(3000, num_warehouses * 200));

    % Adaptive strategy parameters
    stagnation_threshold = max(50, max_iterations * 0.1); % 10% of iterations
    exploration_threshold = 0.01; % Improvement rate threshold
    last_improvement = 0;
    current_strategy = 'exploration';

    % Initialize particles using hybrid greedy strategies
    fprintf('Initializing particles with hybrid greedy strategies...\n');
    particles = hybridGreedyInitialization(num_particles, data, distances, num_vehicles, max_trips, num_warehouses);

    velocities = cell(num_particles, 1);
    personal_best = cell(num_particles, 1);
    personal_best_fitness = inf(num_particles, 1);

    for p = 1:num_particles
        velocities{p} = zeros(num_vehicles, max_trips);
        personal_best{p} = particles{p};
    end

    global_best = particles{1};
    global_best_fitness = inf;
    previous_best_fitness = inf;

    fprintf('Starting Adaptive Multi-Strategy PSO with %d particles, %d iterations\n', num_particles, max_iterations);

    % AMPSO main loop
    for iter = 1:max_iterations
        % Evaluate all particles
        for p = 1:num_particles
            fitness = fitnessFunction(particles{p}, data, distances, paths);

            % Update personal best
            if fitness < personal_best_fitness(p)
                personal_best{p} = particles{p};
                personal_best_fitness(p) = fitness;
            end

            % Update global best
            if fitness < global_best_fitness
                global_best = particles{p};
                global_best_fitness = fitness;
                last_improvement = iter;
            end
        end

        % Calculate improvement rate
        improvement_rate = abs(previous_best_fitness - global_best_fitness) / max(1, previous_best_fitness);
        previous_best_fitness = global_best_fitness;

        % Adaptive strategy selection
        [w, c1, c2, strategy_changed] = adaptiveStrategySelection(iter, max_iterations, ...
            last_improvement, stagnation_threshold, improvement_rate, exploration_threshold, current_strategy);

        if strategy_changed
            current_strategy = getStrategyName(w, c1, c2);
            if strcmp(current_strategy, 'restart')
                % Restart strategy: reinitialize 80% of particles
                restart_count = floor(0.8 * num_particles);
                for p = (num_particles - restart_count + 1):num_particles
                    particles{p} = initializeParticleGreedy(num_vehicles, max_trips, num_warehouses, data, distances);
                    velocities{p} = zeros(num_vehicles, max_trips);
                end
                last_improvement = iter;
                fprintf('Iteration %d: Restart strategy activated\n', iter);
            end
        end

        % Update particles with adaptive parameters
        for p = 1:num_particles
            % Update velocity
            r1 = rand(size(particles{p}));
            r2 = rand(size(particles{p}));

            velocities{p} = w * velocities{p} + ...
                c1 * r1 .* (personal_best{p} - particles{p}) + ...
                c2 * r2 .* (global_best - particles{p});

            % Update position
            particles{p} = particles{p} + velocities{p};

            % Apply constraints
            particles{p} = round(particles{p});
            particles{p} = max(-1, min(num_warehouses, particles{p}));
        end

        if mod(iter, 500) == 0
            fprintf('Iteration %d: Best fitness = %.2f, Strategy = %s\n', iter, global_best_fitness, current_strategy);
        end
    end

    best_schedule = global_best;
    best_fitness = global_best_fitness;

    fprintf('Adaptive Multi-Strategy PSO completed. Best fitness: %.2f\n', best_fitness);
end

function [w, c1, c2, strategy_changed] = adaptiveStrategySelection(iter, max_iterations, ...
    last_improvement, stagnation_threshold, improvement_rate, exploration_threshold, current_strategy)
% ADAPTIVESTRATEGYSELETION - Select appropriate PSO strategy based on convergence state

    strategy_changed = false;
    stagnation_period = iter - last_improvement;

    % Determine new strategy
    if stagnation_period > stagnation_threshold
        % Restart strategy
        new_strategy = 'restart';
        w = 0.9; c1 = 2.0; c2 = 1.0;
        strategy_changed = true;
    elseif iter <= 0.3 * max_iterations || improvement_rate > exploration_threshold
        % Exploration strategy
        new_strategy = 'exploration';
        w = 0.9; c1 = 2.0; c2 = 1.0;
        if ~strcmp(current_strategy, 'exploration')
            strategy_changed = true;
        end
    else
        % Exploitation strategy
        new_strategy = 'exploitation';
        w = 0.4; c1 = 1.0; c2 = 2.0;
        if ~strcmp(current_strategy, 'exploitation')
            strategy_changed = true;
        end
    end
end

function strategy_name = getStrategyName(w, c1, c2)
% GETSTRATEGYNAME - Get strategy name based on parameters
    if w == 0.9 && c1 == 2.0 && c2 == 1.0
        if exist('restart_flag', 'var')
            strategy_name = 'restart';
        else
            strategy_name = 'exploration';
        end
    else
        strategy_name = 'exploitation';
    end
end

function particles = hybridGreedyInitialization(num_particles, data, distances, num_vehicles, max_trips, num_warehouses)
% HYBRIDGREEDYINITIALIZATION - Initialize particles using multiple greedy strategies

    particles = cell(num_particles, 1);

    % Strategy distribution
    nearest_neighbor_count = floor(0.3 * num_particles);
    time_window_count = floor(0.25 * num_particles);
    clustering_count = floor(0.25 * num_particles);
    random_count = num_particles - nearest_neighbor_count - time_window_count - clustering_count;

    particle_idx = 1;

    % 30% Nearest Neighbor Greedy
    for i = 1:nearest_neighbor_count
        particles{particle_idx} = nearestNeighborInit(data, distances, num_vehicles, max_trips, num_warehouses);
        particle_idx = particle_idx + 1;
    end

    % 25% Time Window Priority Greedy
    for i = 1:time_window_count
        particles{particle_idx} = timeWindowInit(data, distances, num_vehicles, max_trips, num_warehouses);
        particle_idx = particle_idx + 1;
    end

    % 25% Clustering-based Greedy
    for i = 1:clustering_count
        particles{particle_idx} = clusteringInit(data, distances, num_vehicles, max_trips, num_warehouses);
        particle_idx = particle_idx + 1;
    end

    % 20% Random initialization (for diversity)
    for i = 1:random_count
        particles{particle_idx} = initializeParticleGreedy(num_vehicles, max_trips, num_warehouses, data, distances);
        particle_idx = particle_idx + 1;
    end
end

function particle = nearestNeighborInit(data, distances, num_vehicles, max_trips, num_warehouses)
% NEARESTNEIGHBORINIT - Nearest neighbor greedy initialization

    particle = -ones(num_vehicles, max_trips);
    unvisited_warehouses = 1:num_warehouses;

    for v = 1:num_vehicles
        vehicle = data.vehicles(v);
        current_base_idx = find([data.bases.id] == vehicle.initial_base);
        trip_count = 0;

        while ~isempty(unvisited_warehouses) && trip_count < max_trips
            % Find nearest unvisited warehouse
            min_dist = inf;
            nearest_warehouse = -1;

            for w = unvisited_warehouses
                % Find warehouse index for distances array
                w_idx = find([data.warehouses.id] == w);
                if ~isempty(w_idx)
                    dist = distances(current_base_idx, w_idx);
                    if dist < min_dist
                        min_dist = dist;
                        nearest_warehouse = w;
                    end
                end
            end

            if nearest_warehouse ~= -1
                trip_count = trip_count + 1;
                particle(v, trip_count) = nearest_warehouse;
                unvisited_warehouses(unvisited_warehouses == nearest_warehouse) = [];
            else
                break;
            end
        end
    end
end

function particle = timeWindowInit(data, distances, num_vehicles, max_trips, num_warehouses)
% TIMEWINDOWINIT - Time window priority greedy initialization

    particle = -ones(num_vehicles, max_trips);

    % Sort warehouses by time window urgency (shorter windows first)
    time_windows = zeros(num_warehouses, 1);
    for w = 1:num_warehouses
        time_windows(w) = data.warehouses(w).time_window(2) - data.warehouses(w).time_window(1);
    end
    [~, sorted_indices] = sort(time_windows);

    vehicle_trip_count = zeros(num_vehicles, 1);

    for w_idx = 1:num_warehouses
        w = sorted_indices(w_idx);

        % Find best vehicle for this warehouse (earliest arrival)
        best_vehicle = -1;
        earliest_arrival = inf;

        for v = 1:num_vehicles
            if vehicle_trip_count(v) < max_trips
                vehicle = data.vehicles(v);
                base_idx = find([data.bases.id] == vehicle.initial_base);
                w_idx = find([data.warehouses.id] == w);
                if ~isempty(w_idx)
                    arrival_time = distances(base_idx, w_idx);
                else
                    arrival_time = inf;
                end

                if arrival_time < earliest_arrival
                    earliest_arrival = arrival_time;
                    best_vehicle = v;
                end
            end
        end

        if best_vehicle ~= -1
            vehicle_trip_count(best_vehicle) = vehicle_trip_count(best_vehicle) + 1;
            particle(best_vehicle, vehicle_trip_count(best_vehicle)) = w;
        end
    end
end

function particle = clusteringInit(data, distances, num_vehicles, max_trips, num_warehouses)
% CLUSTERINGINIT - Clustering-based greedy initialization

    particle = -ones(num_vehicles, max_trips);

    % Extract warehouse positions
    warehouse_positions = zeros(num_warehouses, 2);
    for w = 1:num_warehouses
        warehouse_positions(w, :) = [data.warehouses(w).x, data.warehouses(w).y];
    end

    % Simple K-means clustering (simplified implementation)
    num_clusters = min(num_vehicles, num_warehouses);
    if num_clusters <= 1
        % Fallback to nearest neighbor if too few warehouses
        particle = nearestNeighborInit(data, distances, num_vehicles, max_trips, num_warehouses);
        return;
    end

    % Initialize cluster centers randomly
    cluster_centers = warehouse_positions(randperm(num_warehouses, num_clusters), :);
    clusters = zeros(num_warehouses, 1);

    % Simple clustering: assign each warehouse to nearest center
    for w = 1:num_warehouses
        min_dist = inf;
        for c = 1:num_clusters
            dist = norm(warehouse_positions(w, :) - cluster_centers(c, :));
            if dist < min_dist
                min_dist = dist;
                clusters(w) = c;
            end
        end
    end

    % Assign vehicles to clusters and use nearest neighbor within clusters
    for v = 1:num_vehicles
        cluster_id = mod(v - 1, num_clusters) + 1;
        cluster_warehouses = find(clusters == cluster_id);

        if ~isempty(cluster_warehouses)
            vehicle = data.vehicles(v);
            base_idx = find([data.bases.id] == vehicle.initial_base);
            trip_count = 0;

            while ~isempty(cluster_warehouses) && trip_count < max_trips
                % Find nearest warehouse in this cluster
                min_dist = inf;
                nearest_warehouse = -1;
                nearest_idx = -1;

                for i = 1:length(cluster_warehouses)
                    w = cluster_warehouses(i);
                    % Find warehouse index for distances array
                    w_idx = find([data.warehouses.id] == w);
                    if ~isempty(w_idx)
                        dist = distances(base_idx, w_idx);
                        if dist < min_dist
                            min_dist = dist;
                            nearest_warehouse = w;
                            nearest_idx = i;
                        end
                    end
                end

                if nearest_warehouse ~= -1
                    trip_count = trip_count + 1;
                    particle(v, trip_count) = nearest_warehouse;
                    cluster_warehouses(nearest_idx) = [];
                else
                    break;
                end
            end
        end
    end
end

function particle = initializeParticleGreedy(num_vehicles, max_trips, num_warehouses, data, distances)
% INITIALIZEPARTICLEGREEDY - Enhanced random initialization with greedy bias

    particle = -ones(num_vehicles, max_trips);
    warehouses_assigned = false(num_warehouses, 1);

    % First pass: ensure all warehouses are assigned with greedy bias
    for v = 1:num_vehicles
        vehicle = data.vehicles(v);
        base_idx = find([data.bases.id] == vehicle.initial_base);
        num_trips = randi([2, max_trips]);

        for t = 1:num_trips
            if rand < 0.9 % 90% chance to assign a warehouse
                unassigned_warehouses = find(~warehouses_assigned);
                if ~isempty(unassigned_warehouses)
                    % Greedy bias: prefer closer warehouses
                    warehouse_distances = distances(base_idx, unassigned_warehouses);
                    [~, sorted_indices] = sort(warehouse_distances);

                    % Select from top 50% closest warehouses
                    top_half = max(1, floor(length(sorted_indices) * 0.5));
                    selected_idx = randi(top_half);
                    warehouse_id = unassigned_warehouses(sorted_indices(selected_idx));

                    warehouses_assigned(warehouse_id) = true;
                else
                    warehouse_id = randi(num_warehouses);
                end
                particle(v, t) = warehouse_id;
            end
        end
    end

    % Second pass: ensure all warehouses are covered
    unassigned_warehouses = find(~warehouses_assigned);
    for i = 1:length(unassigned_warehouses)
        [v, t] = find(particle == -1, 1);
        if ~isempty(v)
            particle(v, t) = unassigned_warehouses(i);
        else
            % Replace a random assignment
            v = randi(num_vehicles);
            t = randi(max_trips);
            particle(v, t) = unassigned_warehouses(i);
        end
    end
end

function particle = initializeParticle(num_vehicles, max_trips, num_warehouses)
% INITIALIZEPARTICLE - Legacy function for compatibility, now calls enhanced version

    % For compatibility, create dummy data structure
    dummy_data.vehicles = struct('initial_base', num2cell(ones(1, num_vehicles)));
    dummy_data.bases.id = 1;
    dummy_distances = ones(1, num_warehouses);

    particle = initializeParticleGreedy(num_vehicles, max_trips, num_warehouses, dummy_data, dummy_distances);
end

function fitness = fitnessFunction(schedule, data, distances, paths)
% FITNESSFUNCTION - Evaluate the fitness of a vehicle schedule

    num_vehicles = size(schedule, 1);
    num_warehouses = length(data.warehouses);

    % Initialize penalties
    time_penalty = 0;
    distance_penalty = 0;
    unfulfilled_demand_penalty = 0;
    vehicle_utilization_imbalance = 0;

    % Track warehouse demand fulfillment
    warehouse_fulfillment = zeros(num_warehouses, 1);
    vehicle_utilization = zeros(num_vehicles, 1);

    % Process each vehicle's schedule
    for v = 1:num_vehicles
        vehicle = data.vehicles(v);
        current_time = 0;
        current_base = vehicle.initial_base;
        total_distance = 0;

        % Process each trip
        for t = 1:size(schedule, 2)
            warehouse_id = schedule(v, t);

            if warehouse_id > 0 && warehouse_id <= num_warehouses
                % Find the base index for current_base
                base_idx = find([data.bases.id] == current_base);

                % Calculate travel time and distance
                warehouse_idx = find([data.warehouses.id] == warehouse_id);
                travel_distance = distances(base_idx, warehouse_idx);
                travel_time = travel_distance; % Assume speed = 1 unit/time

                arrival_time = current_time + travel_time;

                % Check time window constraint
                warehouse_idx = find([data.warehouses.id] == warehouse_id);
                warehouse = data.warehouses(warehouse_idx);
                time_window = warehouse.time_window;

                if arrival_time < time_window(1)
                    time_penalty = time_penalty + (time_window(1) - arrival_time) * 10;
                elseif arrival_time > time_window(2)
                    time_penalty = time_penalty + (arrival_time - time_window(2)) * 20;
                end

                % Add distance penalty
                distance_penalty = distance_penalty + travel_distance * vehicle.cost_per_distance;

                % Update fulfillment (simplified: assume full capacity used)
                warehouse_idx = find([data.warehouses.id] == warehouse_id);
                if ~isempty(warehouse_idx) && warehouse_idx <= length(warehouse_fulfillment)
                    warehouse_fulfillment(warehouse_idx) = warehouse_fulfillment(warehouse_idx) + 1;
                end
                vehicle_utilization(v) = vehicle_utilization(v) + 1;

                % Update current state
                current_time = arrival_time + 1; % Service time = 1
                total_distance = total_distance + travel_distance;
            end
        end
    end

    % Calculate unfulfilled demand penalty (much heavier penalty)
    for w = 1:num_warehouses
        if warehouse_fulfillment(w) == 0
            unfulfilled_demand_penalty = unfulfilled_demand_penalty + 10000; % Much heavier penalty for unvisited warehouses
        end
    end

    % Calculate vehicle utilization imbalance
    mean_utilization = mean(vehicle_utilization);
    vehicle_utilization_imbalance = sum((vehicle_utilization - mean_utilization).^2);

    % Total fitness (lower is better)
    fitness = time_penalty + distance_penalty + unfulfilled_demand_penalty + vehicle_utilization_imbalance;
end

function detailed_schedule = generateDetailedSchedule(data, best_schedule, distances, paths)
% GENERATEDETAILEDSCHEDULE - Generate detailed schedule from optimized solution

    num_vehicles = size(best_schedule, 1);

    % Initialize output structure
    detailed_schedule = struct();
    detailed_schedule.schedule = [];
    detailed_schedule.completion_status = [];
    detailed_schedule.performance_metrics = struct();

    % Track warehouse completion
    warehouse_completion = struct();
    for w = 1:length(data.warehouses)
        warehouse_completion(w).warehouse_id = data.warehouses(w).id;  % Use actual warehouse ID
        warehouse_completion(w).demands_fulfilled = [];
        warehouse_completion(w).time_window_status = 'not_visited';
        warehouse_completion(w).actual_arrival_time = -1;
    end

    total_cost = 0;
    time_penalty = 0;
    distance_penalty = 0;
    unfulfilled_demand_penalty = 0;

    % Process each vehicle
    for v = 1:num_vehicles
        vehicle = data.vehicles(v);
        vehicle_schedule = struct();
        vehicle_schedule.vehicle_id = vehicle.id;
        vehicle_schedule.route = [];

        current_time = 0;
        current_base = vehicle.initial_base;
        trip_count = 0;

        % Process each trip
        for t = 1:size(best_schedule, 2)
            warehouse_id = best_schedule(v, t);

            if warehouse_id > 0 && warehouse_id <= length(data.warehouses)
                trip_count = trip_count + 1;

                % Find base index
                base_idx = find([data.bases.id] == current_base);

                % Calculate travel details
                warehouse_idx = find([data.warehouses.id] == warehouse_id);
                travel_distance = distances(base_idx, warehouse_idx);
                travel_time = travel_distance;
                departure_time = current_time;
                arrival_time = current_time + travel_time;

                % Create trip record
                trip = struct();
                trip.trip = trip_count;
                trip.base_id = current_base;
                trip.warehouse_id = warehouse_id;
                trip.departure_time = departure_time;
                trip.arrival_time = arrival_time;

                vehicle_schedule.route = [vehicle_schedule.route; trip];

                % Update warehouse completion status
                warehouse_idx = find([data.warehouses.id] == warehouse_id);
                warehouse = data.warehouses(warehouse_idx);
                time_window = warehouse.time_window;

                if arrival_time >= time_window(1) && arrival_time <= time_window(2)
                    warehouse_completion(warehouse_idx).time_window_status = 'on_time';
                elseif arrival_time < time_window(1)
                    warehouse_completion(warehouse_idx).time_window_status = 'early';
                    time_penalty = time_penalty + (time_window(1) - arrival_time) * 10;
                else
                    warehouse_completion(warehouse_idx).time_window_status = 'late';
                    time_penalty = time_penalty + (arrival_time - time_window(2)) * 20;
                end

                warehouse_completion(warehouse_idx).actual_arrival_time = arrival_time;

                % Add distance cost
                distance_penalty = distance_penalty + travel_distance * vehicle.cost_per_distance;

                % Update current state
                current_time = arrival_time + 1; % Service time
            end
        end

        detailed_schedule.schedule = [detailed_schedule.schedule; vehicle_schedule];
    end

    % Check for unfulfilled warehouses (much heavier penalty)
    for w = 1:length(data.warehouses)
        if strcmp(warehouse_completion(w).time_window_status, 'not_visited')
            unfulfilled_demand_penalty = unfulfilled_demand_penalty + 10000; % Match the fitness function penalty
        end

        % Simplified demand fulfillment (assume all demands met if visited)
        if ~strcmp(warehouse_completion(w).time_window_status, 'not_visited')
            warehouse = data.warehouses(w);
            for d = 1:length(warehouse.demands)
                demand_fulfilled = struct();
                demand_fulfilled.type = warehouse.demands(d).type;
                demand_fulfilled.amount = warehouse.demands(d).amount;
                demand_fulfilled.completion_rate = 1.0; % Simplified: 100% completion
                warehouse_completion(w).demands_fulfilled = [warehouse_completion(w).demands_fulfilled; demand_fulfilled];
            end
        end
    end

    detailed_schedule.completion_status = warehouse_completion;

    % Calculate performance metrics
    total_cost = time_penalty + distance_penalty + unfulfilled_demand_penalty;

    % Vehicle utilization imbalance
    vehicle_trips = zeros(num_vehicles, 1);
    for v = 1:num_vehicles
        vehicle_trips(v) = length(detailed_schedule.schedule(v).route);
    end
    vehicle_utilization_imbalance = sum((vehicle_trips - mean(vehicle_trips)).^2);

    detailed_schedule.performance_metrics.total_cost = total_cost;
    detailed_schedule.performance_metrics.time_penalty = time_penalty;
    detailed_schedule.performance_metrics.distance_penalty = distance_penalty;
    detailed_schedule.performance_metrics.unfulfilled_demand_penalty = unfulfilled_demand_penalty;
    detailed_schedule.performance_metrics.vehicle_utilization_imbalance = vehicle_utilization_imbalance;

    fprintf('Generated detailed schedule with total cost: %.2f\n', total_cost);
end

function createVisualizationsTwo(grid, data, detailed_schedule, paths, grid_image_file, route_image_file)
% CREATEVISUALIZATIONSTWO - Create two different visualizations of the routing solution
%
% Inputs:
%   grid_image_file  - Grid-based path visualization showing actual shortest paths
%   route_image_file - Direct route visualization showing vehicle-warehouse assignments

    % Create first visualization: Grid-based paths
    createGridPathVisualization(grid, data, detailed_schedule, paths, grid_image_file);

    % Create second visualization: Direct routes
    createDirectRouteVisualization(data, detailed_schedule, route_image_file);
end

function createGridPathVisualization(grid, data, detailed_schedule, paths, image_file)
% CREATEGRIDPATHVISUALIZATION - Create grid-based path visualization

    [rows, cols] = size(grid);

    % Create figure
    figure('Visible', 'off', 'Position', [100, 100, 800, 800]);
    hold on;

    % Use grid (same as cheliangdiaodu) instead of manual grid lines
    % Grid will be added later after axis setup

    % Define colors for different vehicles
    colors = {
        [1, 0, 0],     % Red
        [0, 0, 1],     % Blue
        [0, 1, 0],     % Green
        [1, 0, 1],     % Magenta
        [0, 1, 1],     % Cyan
        [1, 1, 0],     % Yellow
        [0, 0, 0],     % Black
        [0.8, 0.4, 0], % Orange
        [0.6, 0.2, 0.8] % Purple
    };

    % Generate additional colors if needed
    num_vehicles = length(detailed_schedule.schedule);
    if num_vehicles > length(colors)
        for i = (length(colors)+1):num_vehicles
            colors{i} = rand(1, 3);
        end
    end

    % Debug: print colors array size
    fprintf('Debug: colors array size = %d, num_vehicles = %d\n', length(colors), num_vehicles);

    % Store text handles for later positioning on top
    text_handles = [];

    % Plot bases (use same coordinate system as cheliangdiaodu)
    for i = 1:length(data.bases)
        base = data.bases(i);
        plot(base.y, base.x, 's', 'MarkerSize', 20, 'MarkerFaceColor', 'red', ...
            'MarkerEdgeColor', 'black', 'LineWidth', 3);
        h = text(base.y, base.x-0.4, sprintf('jidi %d', base.id), 'HorizontalAlignment', 'center', ...
            'VerticalAlignment', 'middle', 'FontSize', 9, 'FontWeight', 'bold', 'Color', 'black', ...
            'BackgroundColor', 'white', 'EdgeColor', 'black', 'Margin', 1);
        text_handles = [text_handles, h];
    end

    % Plot warehouses (use same coordinate system as cheliangdiaodu)
    for i = 1:length(data.warehouses)
        warehouse = data.warehouses(i);
        plot(warehouse.y, warehouse.x, 'o', 'MarkerSize', 16, 'MarkerFaceColor', 'blue', ...
            'MarkerEdgeColor', 'black', 'LineWidth', 2);
        h = text(warehouse.y, warehouse.x-0.4, sprintf('ck %d', warehouse.id), 'HorizontalAlignment', 'center', ...
            'VerticalAlignment', 'middle', 'FontSize', 8, 'FontWeight', 'bold', 'Color', 'black', ...
            'BackgroundColor', 'white', 'EdgeColor', 'black', 'Margin', 1);
        text_handles = [text_handles, h];
    end

    % Plot vehicle routes with actual shortest paths
    for v = 1:length(detailed_schedule.schedule)
        vehicle_schedule = detailed_schedule.schedule(v);
        color = colors{v};

        for t = 1:length(vehicle_schedule.route)
            trip = vehicle_schedule.route(t);

            % Find base index
            base_idx = find([data.bases.id] == trip.base_id);

            % Find warehouse index (not ID!)
            warehouse_idx = find([data.warehouses.id] == trip.warehouse_id);

            % Get path from paths cell array using indices
            path = paths{base_idx, warehouse_idx};

            if ~isempty(path)
                % Plot path (use same coordinate system as cheliangdiaodu: y,x)
                for p = 1:size(path, 1)-1
                    % path contains [row, col], we need to plot as [col, row] = [y, x]
                    x1 = path(p, 2);    % col -> x
                    y1 = path(p, 1);    % row -> y
                    x2 = path(p+1, 2);  % col -> x
                    y2 = path(p+1, 1);  % row -> y
                    plot([x1, x2], [y1, y2], ...
                        'Color', color, 'LineWidth', 3);
                end

                % No arrows needed for lujingguihua - just show the path
            end
        end
    end

    % Move all text to the top layer so they appear above all lines and arrows
    for i = 1:length(text_handles)
        uistack(text_handles(i), 'top');
    end

    % Set axis properties (use same dynamic calculation as cheliangdiaodu)
    axis equal;

    % Calculate appropriate axis limits with padding (same as cheliangdiaodu)
    all_x = [arrayfun(@(b) b.x, data.bases), arrayfun(@(w) w.x, data.warehouses)];
    all_y = [arrayfun(@(b) b.y, data.bases), arrayfun(@(w) w.y, data.warehouses)];

    x_range = max(all_x) - min(all_x);
    y_range = max(all_y) - min(all_y);
    padding = max(x_range, y_range) * 0.1;

    xlim([min(all_y) - padding, max(all_y) + padding]);
    ylim([min(all_x) - padding, max(all_x) + padding]);
    set(gca, 'YDir', 'reverse');

    % Add title and labels
    title('lujingguihua', 'FontSize', 16, 'FontWeight', 'bold');
    xlabel('x', 'FontSize', 12);
    ylabel('y', 'FontSize', 12);

    % Create legend
    legend_entries = {};
    legend_handles = [];

    % Add base and warehouse to legend
    h1 = plot(NaN, NaN, 's', 'MarkerSize', 10, 'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'black');
    h2 = plot(NaN, NaN, 'o', 'MarkerSize', 10, 'MarkerFaceColor', 'blue', 'MarkerEdgeColor', 'black');
    legend_handles = [legend_handles, h1, h2];
    legend_entries = [legend_entries, {'chugongjidi', 'yezhancangku'}];

    % Add vehicle routes to legend
    for v = 1:min(length(detailed_schedule.schedule), 9)
        color = colors{v};
        h = plot(NaN, NaN, 'Color', color, 'LineWidth', 3);
        legend_handles = [legend_handles, h];
        legend_entries = [legend_entries, {sprintf('cheliang %d', v)}];
    end

    legend(legend_handles, legend_entries, 'Location', 'bestoutside');

    % Add grid for reference (same as cheliangdiaodu)
    grid('on');
    set(gca, 'GridAlpha', 0.3);

    % Save figure
    print(gcf, image_file, '-dpng', '-r300');
    close(gcf);

    fprintf('Grid-based visualization saved to: %s\n', image_file);
end

function createDirectRouteVisualization(data, detailed_schedule, image_file)
% CREATEDIRECTROUTEVISUALIZATION - Create direct route visualization with straight arrows

    % Create figure
    figure('Visible', 'off', 'Position', [100, 100, 1000, 800]);
    hold on;

    % Define colors for different vehicles
    colors = {
        [1, 0, 0],     % Red
        [0, 0, 1],     % Blue
        [0, 1, 0],     % Green
        [1, 0, 1],     % Magenta
        [0, 1, 1],     % Cyan
        [1, 1, 0],     % Yellow
        [0, 0, 0],     % Black
        [0.8, 0.4, 0], % Orange
        [0.6, 0.2, 0.8] % Purple
    };

    % Generate additional colors if needed
    num_vehicles = length(detailed_schedule.schedule);
    if num_vehicles > length(colors)
        for i = (length(colors)+1):num_vehicles
            colors{i} = rand(1, 3);
        end
    end

    % Store text handles for later positioning on top
    text_handles = [];

    % Plot bases
    for i = 1:length(data.bases)
        base = data.bases(i);
        plot(base.y, base.x, 's', 'MarkerSize', 20, 'MarkerFaceColor', 'red', ...
            'MarkerEdgeColor', 'black', 'LineWidth', 3);
        h = text(base.y, base.x-0.4, sprintf('jidi %d', base.id), 'HorizontalAlignment', 'center', ...
            'VerticalAlignment', 'middle', 'FontSize', 9, 'FontWeight', 'bold', 'Color', 'black', ...
            'BackgroundColor', 'white', 'EdgeColor', 'black', 'Margin', 1);
        text_handles = [text_handles, h];
    end

    % Plot warehouses
    for i = 1:length(data.warehouses)
        warehouse = data.warehouses(i);
        plot(warehouse.y, warehouse.x, 'o', 'MarkerSize', 16, 'MarkerFaceColor', 'blue', ...
            'MarkerEdgeColor', 'black', 'LineWidth', 2);
        h = text(warehouse.y, warehouse.x-0.4, sprintf('ck %d', warehouse.id), 'HorizontalAlignment', 'center', ...
            'VerticalAlignment', 'middle', 'FontSize', 8, 'FontWeight', 'bold', 'Color', 'black', ...
            'BackgroundColor', 'white', 'EdgeColor', 'black', 'Margin', 1);
        text_handles = [text_handles, h];
    end

    % Plot direct vehicle routes with arrows
    for v = 1:length(detailed_schedule.schedule)
        vehicle_schedule = detailed_schedule.schedule(v);
        color = colors{v};

        for t = 1:length(vehicle_schedule.route)
            trip = vehicle_schedule.route(t);

            % Find base and warehouse positions
            base_idx = find([data.bases.id] == trip.base_id);
            base_pos = [data.bases(base_idx).x, data.bases(base_idx).y];
            warehouse_idx = find([data.warehouses.id] == trip.warehouse_id);
            warehouse_pos = [data.warehouses(warehouse_idx).x, data.warehouses(warehouse_idx).y];

            % Calculate direction vector
            dx = warehouse_pos(2) - base_pos(2); % y direction (column)
            dy = warehouse_pos(1) - base_pos(1); % x direction (row)

            % Draw arrow from center to center (no offset)
            start_x = base_pos(2);
            start_y = base_pos(1);
            end_x = warehouse_pos(2);
            end_y = warehouse_pos(1);

            % Calculate full distance for normalization
            arrow_length = sqrt(dx^2 + dy^2);

            % Calculate adaptive MaxHeadSize to ensure consistent absolute head size
            target_head_size = 0.8;  % Target absolute head size
            if arrow_length > 0
                adaptive_head_size = min(target_head_size / arrow_length, 0.8);  % Cap at 0.8 to avoid oversized heads
            else
                adaptive_head_size = 0.2;
            end

            % Draw arrow with adaptive head size for consistency
            arrow_handle = quiver(start_x, start_y, dx, dy, ...
                0, 'Color', color, 'LineWidth', 3, 'MaxHeadSize', adaptive_head_size, 'AutoScale', 'off');

            % Add trip number label at midpoint
            mid_x = (start_x + end_x) / 2;
            mid_y = (start_y + end_y) / 2;

            h = text(mid_x, mid_y, sprintf('%d-%d', v, t), ...
                'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                'FontSize', 8, 'FontWeight', 'bold', 'Color', color, ...
                'BackgroundColor', 'white', 'EdgeColor', color, 'Margin', 2);
            text_handles = [text_handles, h];
        end
    end

    % Move all text to the top layer so they appear above all lines and arrows
    for i = 1:length(text_handles)
        uistack(text_handles(i), 'top');
    end

    % Set axis properties
    axis equal;

    % Calculate appropriate axis limits with padding
    all_x = [arrayfun(@(b) b.x, data.bases), arrayfun(@(w) w.x, data.warehouses)];
    all_y = [arrayfun(@(b) b.y, data.bases), arrayfun(@(w) w.y, data.warehouses)];

    x_range = max(all_x) - min(all_x);
    y_range = max(all_y) - min(all_y);
    padding = max(x_range, y_range) * 0.1;

    xlim([min(all_y) - padding, max(all_y) + padding]);
    ylim([min(all_x) - padding, max(all_x) + padding]);
    set(gca, 'YDir', 'reverse');

    % Add title and labels
    title('cheliangdiaodu', 'FontSize', 16, 'FontWeight', 'bold');
    xlabel('x', 'FontSize', 12);
    ylabel('y', 'FontSize', 12);

    % Create legend
    legend_entries = {};
    legend_handles = [];

    % Add base and warehouse to legend
    h1 = plot(NaN, NaN, 's', 'MarkerSize', 15, 'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'black');
    h2 = plot(NaN, NaN, 'o', 'MarkerSize', 12, 'MarkerFaceColor', 'blue', 'MarkerEdgeColor', 'black');
    legend_handles = [legend_handles, h1, h2];
    legend_entries = [legend_entries, {'chugongjidi', 'yezhancangku'}];

    % Add vehicle routes to legend
    for v = 1:min(length(detailed_schedule.schedule), 9)
        color = colors{v};
        h = plot(NaN, NaN, 'Color', color, 'LineWidth', 4);
        legend_handles = [legend_handles, h];
        legend_entries = [legend_entries, {sprintf('cheliang %d', v)}];
    end

    legend(legend_handles, legend_entries, 'Location', 'bestoutside');

    % Add grid for reference
    grid on;
    set(gca, 'GridAlpha', 0.3);

    % Save figure
    print(gcf, image_file, '-dpng', '-r300');
    close(gcf);

    fprintf('Direct route visualization saved to: %s\n', image_file);
end

function writeOutput(detailed_schedule, output_file)
% WRITEOUTPUT - Write detailed schedule to JSON file

    try
        % Convert MATLAB struct to JSON
        json_str = jsonencode(detailed_schedule, 'PrettyPrint', true);

        % Write to file
        fid = fopen(output_file, 'w');
        if fid == -1
            error('Cannot open output file: %s', output_file);
        end

        fprintf(fid, '%s', json_str);
        fclose(fid);

        fprintf('Output written to: %s\n', output_file);

    catch ME
        fprintf('Error writing output file: %s\n', ME.message);
        rethrow(ME);
    end
end
