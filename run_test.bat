@echo off
echo ========================================
echo MATLAB车辆路径规划系统Java测试脚本
echo ========================================
echo.

REM 检查Java环境
echo 1. 检查Java环境...
java -version
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到Java环境，请确保Java已正确安装并配置PATH
    pause
    exit /b 1
)
echo ✓ Java环境检查通过
echo.

REM 编译Java程序
echo 2. 编译Java测试程序...
javac SimpleVehicleRoutingTest.java
if %ERRORLEVEL% neq 0 (
    echo 错误: Java程序编译失败
    pause
    exit /b 1
)
echo ✓ Java程序编译成功
echo.

REM 检查必要文件
echo 3. 检查必要文件...
if not exist "vehicle_routing_system.jar" (
    echo 错误: 找不到vehicle_routing_system.jar
    echo 请确保MATLAB生成的jar包在当前目录下
    pause
    exit /b 1
)
echo ✓ 找到jar包: vehicle_routing_system.jar

if not exist "input_large.json" (
    echo 警告: 找不到input_large.json，将使用默认输入
    echo 请确保输入JSON文件在当前目录下
)
echo.

REM 运行测试
echo 4. 运行Java测试程序...
echo ----------------------------------------
java SimpleVehicleRoutingTest
echo ----------------------------------------
echo.

REM 检查结果
echo 5. 测试完成，检查生成的文件...
if exist "output_java_test.json" (
    echo ✓ 找到输出文件: output_java_test.json
) else (
    echo ✗ 未找到输出文件: output_java_test.json
)

if exist "visualization_java_test.png" (
    echo ✓ 找到可视化文件: visualization_java_test.png
) else (
    echo ✗ 未找到可视化文件: visualization_java_test.png
)

echo.
echo 测试脚本执行完成！
pause
