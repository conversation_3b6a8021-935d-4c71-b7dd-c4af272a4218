# MATLAB 车辆路径规划与调度系统

## 项目概述

这是一个基于MATLAB的单文件车辆路径规划与调度系统，用于解决物流配送中的复杂优化问题。系统使用粒子群优化算法(PSO)来优化车辆调度方案，并提供详细的路径可视化。

## 系统特性

- **多基地多仓库支持**：支持多个供应基地和配送仓库
- **多车辆调度**：支持不同载重能力和成本的多辆车
- **时间窗约束**：考虑仓库的时间窗限制
- **多物资类型**：支持不同类型物资的需求和载重限制
- **智能优化**：使用PSO算法优化总成本
- **路径可视化**：生成PNG格式的路径规划图
- **详细报告**：输出JSON格式的详细调度方案

## 文件结构

```
├── vehicle_routing_system.m    # 主程序文件
├── input_example.json         # 小规模示例输入
├── input_large.json          # 大规模测试输入 (3基地+15仓库+9车辆)
├── test_vehicle_routing.m    # 基本测试脚本
├── test_large_scale.m        # 大规模测试脚本
└── README.md                 # 说明文档
```

## 快速开始

### 1. 运行小规模测试
```matlab
test_vehicle_routing
```

### 2. 运行大规模测试
```matlab
test_large_scale
```

### 3. 自定义运行
```matlab
vehicle_routing_system('your_input.json', 'output.json', 'grid_paths.png', 'direct_routes.png')
```

## 输入数据格式

### JSON输入文件结构
```json
{
  "grid_size": 10,
  "bases": [
    {"id": 1, "x": 2, "y": 2}
  ],
  "warehouses": [
    {
      "id": 1, 
      "x": 3, "y": 7,
      "demands": [
        {"type": "A", "amount": 50}
      ],
      "time_window": [5, 15]
    }
  ],
  "vehicles": [
    {
      "id": 1,
      "capacity": {"A": 100},
      "cost_per_distance": 2.5,
      "initial_base": 1
    }
  ],
  "random_seed": 42
}
```

## 输出结果

### 1. JSON调度方案 (output.json)
- 详细的车辆路线安排
- 仓库完成状态
- 性能指标分析

### 2. 双PNG可视化图像
#### 网格路径图 (grid_paths.png)
- 显示网格环境中的实际最短路径
- 考虑边权重的真实路径规划
- 基地和仓库位置标记
- 不同颜色的车辆路径和方向箭头

#### 直接路线图 (direct_routes.png)
- 显示车辆到仓库的直接分配关系
- 用简洁直线表示车辆任务（无箭头）
- 车辆-行程标签 (V1-T1, V2-T1等)
- 适合快速理解调度方案，视觉效果清爽

## 大规模测试案例

当前的大规模测试包含：
- **网格大小**: 10×10
- **供应基地**: 3个 (位置分散)
- **配送仓库**: 15个 (不同需求和时间窗)
- **车辆数量**: 9辆 (不同载重和成本)
- **物资类型**: 5种 (A, B, C, D, E)

## 算法特点

### 路径规划
- 使用Dijkstra算法计算最短路径
- 考虑网格边权重的随机性
- 支持四连通网格结构

### 车辆调度优化
- 粒子群优化算法(PSO)
- 自适应参数调整
- 多目标优化：时间、距离、需求完成度、车辆平衡

### 目标函数
- **时间惩罚**: 提前/延迟到达的时间成本
- **距离惩罚**: 考虑不同车辆的单位距离成本
- **需求惩罚**: 未满足仓库需求的重度惩罚
- **平衡惩罚**: 车辆使用不均衡的惩罚

## 性能优化

系统会根据问题规模自动调整PSO参数：
- 粒子数量：30-100个 (基于仓库数量)
- 迭代次数：50-200次 (基于问题复杂度)
- 最大行程：动态调整以适应仓库/车辆比例

## 使用建议

1. **小规模测试**: 先用小数据验证功能
2. **参数调整**: 根据实际需求调整时间窗和载重
3. **结果分析**: 关注性能指标和车辆利用率
4. **可视化检查**: 通过PNG图像验证路径合理性

## 系统要求

- MATLAB R2018b或更高版本
- 支持JSON读写功能
- 图像处理工具箱(用于PNG输出)

## 故障排除

如果遇到可视化问题，可能是MATLAB版本兼容性问题。系统已移除了可能不兼容的Alpha属性设置。

## 扩展功能

系统设计为模块化结构，可以轻松扩展：
- 添加新的优化算法
- 支持更多约束条件
- 集成实时数据接口
- 添加更多可视化选项
