% SIMPLE_TEST - Simple test without visualization
clear all;
close all;
clc;

fprintf('=== Simple Vehicle Routing Test ===\n\n');

% Test individual functions
input_file = 'input_example.json';

try
    % Test 1: Parse input
    fprintf('Test 1: Parsing input file...\n');
    data = parseInput(input_file);
    fprintf('✓ Successfully parsed: %d bases, %d warehouses, %d vehicles\n', ...
        length(data.bases), length(data.warehouses), length(data.vehicles));
    
    % Test 2: Generate grid
    fprintf('\nTest 2: Generating grid...\n');
    [grid, edge_weights] = generateGrid(data.grid_size, data.random_seed);
    fprintf('✓ Generated %dx%d grid\n', size(grid, 1), size(grid, 2));
    
    % Test 3: Calculate shortest paths
    fprintf('\nTest 3: Calculating shortest paths...\n');
    [distances, paths] = calculateAllShortestPaths(grid, edge_weights, data.bases, data.warehouses);
    fprintf('✓ Calculated distance matrix: %dx%d\n', size(distances, 1), size(distances, 2));
    fprintf('Sample distances:\n');
    disp(distances);
    
    % Test 4: PSO optimization (reduced iterations for testing)
    fprintf('\nTest 4: PSO optimization (quick test)...\n');
    [best_schedule, best_fitness] = psoSchedulingQuick(data, distances, paths);
    fprintf('✓ PSO completed with fitness: %.2f\n', best_fitness);
    fprintf('Best schedule:\n');
    disp(best_schedule);
    
    % Test 5: Generate detailed schedule
    fprintf('\nTest 5: Generating detailed schedule...\n');
    detailed_schedule = generateDetailedSchedule(data, best_schedule, distances, paths);
    fprintf('✓ Generated detailed schedule\n');
    fprintf('Total cost: %.2f\n', detailed_schedule.performance_metrics.total_cost);
    
    % Test 6: Write output
    fprintf('\nTest 6: Writing output...\n');
    writeOutput(detailed_schedule, 'test_output.json');
    fprintf('✓ Output written successfully\n');
    
    fprintf('\n=== All Tests Passed! ===\n');
    
catch ME
    fprintf('Error: %s\n', ME.message);
    fprintf('Stack trace:\n');
    for i = 1:length(ME.stack)
        fprintf('  %s (line %d)\n', ME.stack(i).name, ME.stack(i).line);
    end
end

function [best_schedule, best_fitness] = psoSchedulingQuick(data, distances, paths)
% Quick PSO for testing (reduced parameters)
    
    num_vehicles = length(data.vehicles);
    num_warehouses = length(data.warehouses);
    max_trips = 5; % Reduced for testing
    
    % PSO parameters (reduced for quick test)
    num_particles = 10;
    max_iterations = 20;
    w = 0.7;
    c1 = 1.5;
    c2 = 1.5;
    
    % Initialize particles
    particles = cell(num_particles, 1);
    velocities = cell(num_particles, 1);
    personal_best = cell(num_particles, 1);
    personal_best_fitness = inf(num_particles, 1);
    
    for p = 1:num_particles
        particles{p} = initializeParticle(num_vehicles, max_trips, num_warehouses);
        velocities{p} = zeros(num_vehicles, max_trips);
        personal_best{p} = particles{p};
    end
    
    global_best = particles{1};
    global_best_fitness = inf;
    
    fprintf('Quick PSO: %d particles, %d iterations\n', num_particles, max_iterations);
    
    % PSO main loop
    for iter = 1:max_iterations
        for p = 1:num_particles
            fitness = fitnessFunction(particles{p}, data, distances, paths);
            
            if fitness < personal_best_fitness(p)
                personal_best{p} = particles{p};
                personal_best_fitness(p) = fitness;
            end
            
            if fitness < global_best_fitness
                global_best = particles{p};
                global_best_fitness = fitness;
            end
        end
        
        % Update particles
        for p = 1:num_particles
            r1 = rand(size(particles{p}));
            r2 = rand(size(particles{p}));
            
            velocities{p} = w * velocities{p} + ...
                c1 * r1 .* (personal_best{p} - particles{p}) + ...
                c2 * r2 .* (global_best - particles{p});
            
            particles{p} = particles{p} + velocities{p};
            particles{p} = round(particles{p});
            particles{p} = max(-1, min(num_warehouses, particles{p}));
        end
        
        if mod(iter, 5) == 0
            fprintf('Iteration %d: Best fitness = %.2f\n', iter, global_best_fitness);
        end
    end
    
    best_schedule = global_best;
    best_fitness = global_best_fitness;
end
