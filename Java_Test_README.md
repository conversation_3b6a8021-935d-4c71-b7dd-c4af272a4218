# MATLAB车辆路径规划系统 Java调用测试

## 📋 文件说明

- `VehicleRoutingTest.java` - 完整版Java测试程序（功能全面）
- `SimpleVehicleRoutingTest.java` - 简化版Java测试程序（推荐使用）
- `run_test.bat` - Windows批处理脚本，一键编译运行
- `Java_Test_README.md` - 本说明文件

## 🚀 快速开始

### 方法1：使用批处理脚本（推荐）

1. **准备文件**：
   ```
   当前目录下需要有：
   ├── vehicle_routing_system.jar    # MATLAB生成的jar包
   ├── input_large.json             # 输入JSON文件
   ├── SimpleVehicleRoutingTest.java # Java测试程序
   └── run_test.bat                 # 批处理脚本
   ```

2. **运行测试**：
   ```bash
   # 双击运行或在命令行执行
   run_test.bat
   ```

### 方法2：手动编译运行

1. **编译Java程序**：
   ```bash
   javac SimpleVehicleRoutingTest.java
   ```

2. **运行测试**：
   ```bash
   java SimpleVehicleRoutingTest
   ```

## 📁 输入输出文件

### 输入文件
- `input_large.json` - 车辆路径规划的输入数据
- 格式参考现有的JSON文件

### 输出文件
- `output_java_test.json` - 优化后的调度方案
- `visualization_java_test.png` - 路径可视化图像

## 🔧 环境要求

### Java环境
- **Java版本**: JDK 8 或更高版本
- **内存**: 建议至少2GB可用内存

### MATLAB Runtime
- **版本**: 与编译jar包的MATLAB版本对应
- **安装**: 从MathWorks官网下载并安装对应版本的MATLAB Runtime
- **环境变量**: 可选设置MCR_ROOT环境变量

## 📊 测试程序功能

### SimpleVehicleRoutingTest.java 功能
1. **环境检查**：
   - 检查jar包是否存在
   - 检查输入JSON文件是否存在
   - 验证Java环境

2. **程序执行**：
   - 调用MATLAB jar包
   - 实时显示程序输出
   - 监控执行时间和退出状态

3. **结果验证**：
   - 检查输出JSON文件是否生成
   - 检查可视化PNG图像是否生成
   - 显示文件大小和内容预览

### VehicleRoutingTest.java 功能（完整版）
- 包含SimpleVehicleRoutingTest的所有功能
- 额外的环境检查（MATLAB Runtime检测）
- 更详细的结果分析
- 自动创建示例输入文件功能

## 🐛 常见问题排查

### 1. "找不到jar包"
```
错误: 找不到jar包 vehicle_routing_system.jar
```
**解决方案**：
- 确保jar包在当前目录下
- 检查文件名是否正确（区分大小写）

### 2. "找不到输入文件"
```
错误: 找不到输入文件 input_large.json
```
**解决方案**：
- 确保输入JSON文件在当前目录下
- 或修改Java代码中的文件名

### 3. "程序执行失败"
```
退出码: 1
✗ 程序执行失败!
```
**可能原因**：
- MATLAB Runtime未安装或版本不匹配
- 输入JSON格式错误
- 内存不足

**解决方案**：
- 安装对应版本的MATLAB Runtime
- 检查JSON文件格式
- 增加JVM内存：`java -Xmx4g SimpleVehicleRoutingTest`

### 4. "程序运行超时"
```
程序运行超时，强制终止...
```
**解决方案**：
- 减少输入数据规模
- 增加超时时间（修改Java代码中的5分钟限制）
- 检查是否有死循环

## 📈 性能优化建议

### JVM参数优化
```bash
# 增加内存
java -Xmx4g -Xms2g SimpleVehicleRoutingTest

# 启用并行GC
java -XX:+UseParallelGC SimpleVehicleRoutingTest

# 完整优化命令
java -Xmx4g -Xms2g -XX:+UseParallelGC -XX:+UseParallelOldGC SimpleVehicleRoutingTest
```

### 输入数据建议
- **小规模测试**: 3基地 + 5仓库 + 3车辆
- **中规模测试**: 3基地 + 10仓库 + 6车辆  
- **大规模测试**: 3基地 + 15仓库 + 9车辆

## 📝 自定义修改

### 修改文件路径
在Java代码中修改以下变量：
```java
String jarFile = "your_jar_name.jar";
String inputFile = "your_input.json";
String outputFile = "your_output.json";
String imageFile = "your_image.png";
```

### 修改超时时间
```java
// 将5分钟改为其他时间
boolean finished = process.waitFor(10, TimeUnit.MINUTES);
```

### 添加JVM参数
```java
String command = "java -Xmx4g -jar " + jarFile + " " + inputFile + " " + outputFile + " " + imageFile;
```

## 📞 技术支持

如果遇到问题，请检查：
1. Java版本是否兼容
2. MATLAB Runtime是否正确安装
3. 输入JSON文件格式是否正确
4. 系统内存是否充足

建议先用小规模数据测试，确认环境配置正确后再使用大规模数据。
