% TEST_LARGE_SCALE - Test script for large scale vehicle routing system
%
% This script tests the vehicle routing system with large scale data:
% 3 bases, 15 warehouses, 9 vehicles on 10x10 grid

clear all;
close all;
clc;

fprintf('=== Large Scale Vehicle Routing System Test ===\n\n');

% Define file paths
input_file = 'input_large.json';
output_file = 'output_large.json';
grid_image_file = 'routing_grid_paths.png';
route_image_file = 'routing_direct_routes.png';

% Check if input file exists
if ~exist(input_file, 'file')
    error('Input file not found: %s', input_file);
end

try
    % Run the vehicle routing system
    fprintf('Starting large scale vehicle routing system test...\n');
    tic;
    
    vehicle_routing_system(input_file, output_file, grid_image_file, route_image_file);
    
    elapsed_time = toc;
    fprintf('\nLarge scale test completed successfully!\n');
    fprintf('Total execution time: %.2f seconds\n', elapsed_time);
    
    % Verify output files
    if exist(output_file, 'file')
        fprintf('✓ Output JSON file created: %s\n', output_file);
        
        % Display detailed results
        json_text = fileread(output_file);
        result = jsondecode(json_text);
        
        fprintf('\n=== Large Scale Results Summary ===\n');
        fprintf('Total cost: %.2f\n', result.performance_metrics.total_cost);
        fprintf('Time penalty: %.2f\n', result.performance_metrics.time_penalty);
        fprintf('Distance penalty: %.2f\n', result.performance_metrics.distance_penalty);
        fprintf('Unfulfilled demand penalty: %.2f\n', result.performance_metrics.unfulfilled_demand_penalty);
        fprintf('Vehicle utilization imbalance: %.2f\n', result.performance_metrics.vehicle_utilization_imbalance);
        
        fprintf('\n=== Vehicle Schedules Summary ===\n');
        total_trips = 0;
        for i = 1:length(result.schedule)
            schedule = result.schedule(i);
            num_trips = length(schedule.route);
            total_trips = total_trips + num_trips;
            fprintf('Vehicle %d: %d trips\n', schedule.vehicle_id, num_trips);
        end
        fprintf('Total trips across all vehicles: %d\n', total_trips);
        
        fprintf('\n=== Warehouse Completion Status ===\n');
        visited_warehouses = 0;
        on_time_deliveries = 0;
        early_deliveries = 0;
        late_deliveries = 0;
        
        for i = 1:length(result.completion_status)
            status = result.completion_status(i);
            if ~strcmp(status.time_window_status, 'not_visited')
                visited_warehouses = visited_warehouses + 1;
                switch status.time_window_status
                    case 'on_time'
                        on_time_deliveries = on_time_deliveries + 1;
                    case 'early'
                        early_deliveries = early_deliveries + 1;
                    case 'late'
                        late_deliveries = late_deliveries + 1;
                end
            end
        end
        
        fprintf('Warehouses visited: %d/15 (%.1f%%)\n', visited_warehouses, visited_warehouses/15*100);
        fprintf('On-time deliveries: %d (%.1f%%)\n', on_time_deliveries, on_time_deliveries/max(1,visited_warehouses)*100);
        fprintf('Early deliveries: %d (%.1f%%)\n', early_deliveries, early_deliveries/max(1,visited_warehouses)*100);
        fprintf('Late deliveries: %d (%.1f%%)\n', late_deliveries, late_deliveries/max(1,visited_warehouses)*100);
        
        % Show detailed vehicle routes
        fprintf('\n=== Detailed Vehicle Routes ===\n');
        for i = 1:min(3, length(result.schedule)) % Show first 3 vehicles in detail
            schedule = result.schedule(i);
            fprintf('\nVehicle %d detailed route:\n', schedule.vehicle_id);
            if isempty(schedule.route)
                fprintf('  No trips assigned\n');
            else
                for j = 1:length(schedule.route)
                    trip = schedule.route(j);
                    fprintf('  Trip %d: Base %d → Warehouse %d (Depart: %.1f, Arrive: %.1f)\n', ...
                        trip.trip, trip.base_id, trip.warehouse_id, ...
                        trip.departure_time, trip.arrival_time);
                end
            end
        end
        
        if length(result.schedule) > 3
            fprintf('\n... and %d more vehicles\n', length(result.schedule) - 3);
        end
        
    else
        fprintf('✗ Output JSON file not created\n');
    end
    
    if exist(grid_image_file, 'file')
        fprintf('✓ Grid-based visualization created: %s\n', grid_image_file);
    else
        fprintf('✗ Grid-based visualization not created\n');
    end

    if exist(route_image_file, 'file')
        fprintf('✓ Direct route visualization created: %s\n', route_image_file);
    else
        fprintf('✗ Direct route visualization not created\n');
    end
    
    % Performance analysis
    fprintf('\n=== Performance Analysis ===\n');
    if exist('result', 'var')
        efficiency = (15 - result.performance_metrics.unfulfilled_demand_penalty/1000) / 15 * 100;
        fprintf('Delivery efficiency: %.1f%% (warehouses served)\n', efficiency);
        
        avg_cost_per_trip = result.performance_metrics.distance_penalty / max(1, total_trips);
        fprintf('Average cost per trip: %.2f\n', avg_cost_per_trip);
        
        utilization_balance = 1 / (1 + result.performance_metrics.vehicle_utilization_imbalance/100);
        fprintf('Vehicle utilization balance: %.2f (1.0 = perfect balance)\n', utilization_balance);
    end
    
catch ME
    fprintf('Error during large scale test: %s\n', ME.message);
    fprintf('Stack trace:\n');
    for i = 1:length(ME.stack)
        fprintf('  %s (line %d)\n', ME.stack(i).name, ME.stack(i).line);
    end
end

fprintf('\n=== Large Scale Test Complete ===\n');
