import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.text.SimpleDateFormat;

/**
 * 高级版Java测试程序 - 支持配置文件和批量测试
 */
public class AdvancedVehicleRoutingTest {
    
    private Properties config;
    private String timestamp;
    
    public AdvancedVehicleRoutingTest() {
        this.timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        loadConfiguration();
    }
    
    public static void main(String[] args) {
        AdvancedVehicleRoutingTest test = new AdvancedVehicleRoutingTest();
        
        if (args.length > 0) {
            // 支持命令行参数
            test.runWithArgs(args);
        } else {
            // 默认测试模式
            test.runDefaultTest();
        }
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfiguration() {
        config = new Properties();
        
        // 默认配置
        config.setProperty("jar.name", "vehicle_routing_system.jar");
        config.setProperty("input.file", "input_large.json");
        config.setProperty("output.prefix", "output_");
        config.setProperty("image.prefix", "visualization_");
        config.setProperty("timeout.minutes", "10");
        config.setProperty("jvm.memory", "4g");
        config.setProperty("test.iterations", "1");
        
        // 尝试加载外部配置文件
        File configFile = new File("test_config.properties");
        if (configFile.exists()) {
            try (FileInputStream fis = new FileInputStream(configFile)) {
                config.load(fis);
                System.out.println("✓ 加载配置文件: test_config.properties");
            } catch (IOException e) {
                System.out.println("⚠ 加载配置文件失败，使用默认配置");
            }
        }
    }
    
    /**
     * 使用命令行参数运行
     */
    private void runWithArgs(String[] args) {
        System.out.println("=== 命令行模式 ===");
        
        if (args.length < 3) {
            System.out.println("用法: java AdvancedVehicleRoutingTest <input.json> <output.json> <image.png>");
            return;
        }
        
        String inputFile = args[0];
        String outputFile = args[1];
        String imageFile = args[2];
        
        runSingleTest(inputFile, outputFile, imageFile);
    }
    
    /**
     * 默认测试模式
     */
    private void runDefaultTest() {
        System.out.println("=== MATLAB车辆路径规划系统高级测试 ===\n");
        
        int iterations = Integer.parseInt(config.getProperty("test.iterations", "1"));
        
        if (iterations == 1) {
            runSingleTest();
        } else {
            runBatchTest(iterations);
        }
    }
    
    /**
     * 单次测试
     */
    private void runSingleTest() {
        String inputFile = config.getProperty("input.file");
        String outputFile = config.getProperty("output.prefix") + timestamp + ".json";
        String imageFile = config.getProperty("image.prefix") + timestamp + ".png";
        
        runSingleTest(inputFile, outputFile, imageFile);
    }
    
    /**
     * 执行单次测试
     */
    private void runSingleTest(String inputFile, String outputFile, String imageFile) {
        try {
            System.out.println("开始测试...");
            System.out.println("输入文件: " + inputFile);
            System.out.println("输出文件: " + outputFile);
            System.out.println("图像文件: " + imageFile);
            System.out.println();
            
            // 环境检查
            if (!checkEnvironment(inputFile)) {
                return;
            }
            
            // 执行测试
            TestResult result = executeTest(inputFile, outputFile, imageFile);
            
            // 显示结果
            displayResult(result);
            
            // 保存测试报告
            saveTestReport(result);
            
        } catch (Exception e) {
            System.err.println("测试执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 批量测试
     */
    private void runBatchTest(int iterations) {
        System.out.println("=== 批量测试模式 (" + iterations + "次) ===\n");
        
        List<TestResult> results = new ArrayList<>();
        
        for (int i = 1; i <= iterations; i++) {
            System.out.println("--- 第 " + i + " 次测试 ---");
            
            String inputFile = config.getProperty("input.file");
            String outputFile = config.getProperty("output.prefix") + timestamp + "_" + i + ".json";
            String imageFile = config.getProperty("image.prefix") + timestamp + "_" + i + ".png";
            
            try {
                if (checkEnvironment(inputFile)) {
                    TestResult result = executeTest(inputFile, outputFile, imageFile);
                    result.iteration = i;
                    results.add(result);
                    displayResult(result);
                }
            } catch (Exception e) {
                System.err.println("第 " + i + " 次测试失败: " + e.getMessage());
            }
            
            System.out.println();
        }
        
        // 显示批量测试统计
        displayBatchStatistics(results);
        
        // 保存批量测试报告
        saveBatchReport(results);
    }
    
    /**
     * 环境检查
     */
    private boolean checkEnvironment(String inputFile) {
        String jarFile = config.getProperty("jar.name");
        
        if (!new File(jarFile).exists()) {
            System.err.println("✗ 找不到jar包: " + jarFile);
            return false;
        }
        
        if (!new File(inputFile).exists()) {
            System.err.println("✗ 找不到输入文件: " + inputFile);
            return false;
        }
        
        System.out.println("✓ 环境检查通过");
        return true;
    }
    
    /**
     * 执行测试
     */
    private TestResult executeTest(String inputFile, String outputFile, String imageFile) throws Exception {
        TestResult result = new TestResult();
        result.inputFile = inputFile;
        result.outputFile = outputFile;
        result.imageFile = imageFile;
        result.startTime = System.currentTimeMillis();
        
        // 构建命令
        String jarFile = config.getProperty("jar.name");
        String memory = config.getProperty("jvm.memory", "4g");
        String command = String.format("java -Xmx%s -jar %s %s %s %s", 
            memory, jarFile, inputFile, outputFile, imageFile);
        
        System.out.println("执行命令: " + command);
        
        // 执行
        Process process = Runtime.getRuntime().exec(command);
        
        // 捕获输出
        StringBuilder output = new StringBuilder();
        StringBuilder errorOutput = new StringBuilder();
        
        Thread outputThread = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    System.out.println("[MATLAB] " + line);
                }
            } catch (IOException e) {
                System.err.println("读取输出失败: " + e.getMessage());
            }
        });
        
        Thread errorThread = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    errorOutput.append(line).append("\n");
                    System.err.println("[MATLAB ERROR] " + line);
                }
            } catch (IOException e) {
                System.err.println("读取错误输出失败: " + e.getMessage());
            }
        });
        
        outputThread.start();
        errorThread.start();
        
        // 等待完成
        int timeoutMinutes = Integer.parseInt(config.getProperty("timeout.minutes", "10"));
        boolean finished = process.waitFor(timeoutMinutes, TimeUnit.MINUTES);
        
        result.endTime = System.currentTimeMillis();
        result.executionTime = result.endTime - result.startTime;
        result.exitCode = finished ? process.exitValue() : -1;
        result.output = output.toString();
        result.errorOutput = errorOutput.toString();
        result.success = finished && result.exitCode == 0;
        
        if (!finished) {
            process.destroyForcibly();
            result.errorMessage = "执行超时";
        }
        
        // 检查输出文件
        result.outputFileExists = new File(outputFile).exists();
        result.imageFileExists = new File(imageFile).exists();
        
        if (result.outputFileExists) {
            result.outputFileSize = new File(outputFile).length();
        }
        if (result.imageFileExists) {
            result.imageFileSize = new File(imageFile).length();
        }
        
        return result;
    }
    
    /**
     * 显示测试结果
     */
    private void displayResult(TestResult result) {
        System.out.println("\n=== 测试结果 ===");
        System.out.println("执行状态: " + (result.success ? "✓ 成功" : "✗ 失败"));
        System.out.println("退出码: " + result.exitCode);
        System.out.println("执行时间: " + result.executionTime / 1000.0 + " 秒");
        System.out.println("输出文件: " + (result.outputFileExists ? "✓ 已生成 (" + result.outputFileSize + " 字节)" : "✗ 未生成"));
        System.out.println("图像文件: " + (result.imageFileExists ? "✓ 已生成 (" + result.imageFileSize + " 字节)" : "✗ 未生成"));
        
        if (!result.success && result.errorMessage != null) {
            System.out.println("错误信息: " + result.errorMessage);
        }
    }
    
    /**
     * 显示批量测试统计
     */
    private void displayBatchStatistics(List<TestResult> results) {
        System.out.println("=== 批量测试统计 ===");
        
        int successCount = (int) results.stream().mapToInt(r -> r.success ? 1 : 0).sum();
        double avgTime = results.stream().mapToLong(r -> r.executionTime).average().orElse(0) / 1000.0;
        long minTime = results.stream().mapToLong(r -> r.executionTime).min().orElse(0) / 1000;
        long maxTime = results.stream().mapToLong(r -> r.executionTime).max().orElse(0) / 1000;
        
        System.out.println("总测试次数: " + results.size());
        System.out.println("成功次数: " + successCount);
        System.out.println("成功率: " + (successCount * 100.0 / results.size()) + "%");
        System.out.println("平均执行时间: " + avgTime + " 秒");
        System.out.println("最短执行时间: " + minTime + " 秒");
        System.out.println("最长执行时间: " + maxTime + " 秒");
    }
    
    /**
     * 保存测试报告
     */
    private void saveTestReport(TestResult result) {
        try {
            String reportFile = "test_report_" + timestamp + ".txt";
            try (PrintWriter writer = new PrintWriter(new FileWriter(reportFile))) {
                writer.println("=== MATLAB车辆路径规划系统测试报告 ===");
                writer.println("测试时间: " + new Date(result.startTime));
                writer.println("输入文件: " + result.inputFile);
                writer.println("输出文件: " + result.outputFile);
                writer.println("图像文件: " + result.imageFile);
                writer.println("执行状态: " + (result.success ? "成功" : "失败"));
                writer.println("退出码: " + result.exitCode);
                writer.println("执行时间: " + result.executionTime / 1000.0 + " 秒");
                writer.println("输出文件大小: " + result.outputFileSize + " 字节");
                writer.println("图像文件大小: " + result.imageFileSize + " 字节");
                writer.println();
                writer.println("=== 程序输出 ===");
                writer.println(result.output);
                if (!result.errorOutput.isEmpty()) {
                    writer.println();
                    writer.println("=== 错误输出 ===");
                    writer.println(result.errorOutput);
                }
            }
            System.out.println("✓ 测试报告已保存: " + reportFile);
        } catch (IOException e) {
            System.err.println("保存测试报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存批量测试报告
     */
    private void saveBatchReport(List<TestResult> results) {
        try {
            String reportFile = "batch_test_report_" + timestamp + ".txt";
            try (PrintWriter writer = new PrintWriter(new FileWriter(reportFile))) {
                writer.println("=== 批量测试报告 ===");
                writer.println("测试时间: " + new Date());
                writer.println("测试次数: " + results.size());
                writer.println();
                
                for (TestResult result : results) {
                    writer.println("--- 测试 " + result.iteration + " ---");
                    writer.println("状态: " + (result.success ? "成功" : "失败"));
                    writer.println("时间: " + result.executionTime / 1000.0 + " 秒");
                    writer.println("输出: " + result.outputFile);
                    writer.println();
                }
            }
            System.out.println("✓ 批量测试报告已保存: " + reportFile);
        } catch (IOException e) {
            System.err.println("保存批量测试报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试结果数据类
     */
    static class TestResult {
        int iteration;
        String inputFile;
        String outputFile;
        String imageFile;
        long startTime;
        long endTime;
        long executionTime;
        int exitCode;
        boolean success;
        String output;
        String errorOutput;
        String errorMessage;
        boolean outputFileExists;
        boolean imageFileExists;
        long outputFileSize;
        long imageFileSize;
    }
}
