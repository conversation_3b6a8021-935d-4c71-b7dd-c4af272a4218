# MATLAB 路径规划与车辆调度系统实现提示词

## 项目概述
代码里不要含有中文，实现一个单文件MATLAB程序，解决物流配送中的路径规划和车辆调度问题。程序通过JSON文件接收输入参数，输出包括调度方案的JSON文件和路径可视化的PNG图像。

## 系统环境
- 在n×n网格图中分布有a个储供基地、b个仓库和c辆车
- 每个网格节点有四条边，边的权重随机生成
- 节点可能是储供基地、仓库或单纯的路径节点

## 数据结构设计
### 输入JSON格式
```json
{
  "grid_size": n,
  "bases": [
    {"id": 1, "x": x1, "y": y1},
    {"id": 2, "x": x2, "y": y2},
    ...
  ],
  "warehouses": [
    {
      "id": 1, "x": x1, "y": y1,
      "demands": [
        {"type": "A", "amount": a1},
        {"type": "B", "amount": b1},
        ...
      ],
      "time_window": [t_start1, t_end1]
    },
    ...
  ],
  "vehicles": [
    {
      "id": 1,
      "capacity": {
        "A": cap_A1,
        "B": cap_B1,
        ...
      },
      "cost_per_distance": c1,
      "initial_base": base_id1
    },
    ...
  ],
  "random_seed": seed_value
}
```

### 输出JSON格式
```json
{
  "schedule": [
    {
      "vehicle_id": 1,
      "route": [
        {"trip": 1, "base_id": b1, "warehouse_id": w1, "departure_time": dt1, "arrival_time": at1},
        {"trip": 2, "base_id": b2, "warehouse_id": w2, "departure_time": dt2, "arrival_time": at2},
        ...
      ]
    },
    ...
  ],
  "completion_status": [
    {
      "warehouse_id": 1,
      "demands_fulfilled": [
        {"type": "A", "amount": a1, "completion_rate": r1},
        ...
      ],
      "time_window_status": "on_time|early|late",
      "actual_arrival_time": t1
    },
    ...
  ],
  "performance_metrics": {
    "total_cost": cost,
    "time_penalty": tp,
    "distance_penalty": dp,
    "unfulfilled_demand_penalty": udp,
    "vehicle_utilization_imbalance": vui
  }
}
```

## 算法实现要点
1. **路径规划算法**：
   - 使用启发式算法(如A*或Dijkstra)计算储供基地到仓库的最短路径
   - 考虑网格图的四连通特性和边权重

2. **车辆调度算法**：
   - 使用粒子群算法(PSO)优化车辆调度方案
   - 编码方式：每行表示一辆车的行程安排，数字表示仓库ID，-1表示不行动
   - 示例矩阵：`[1,2; 3,-1]` 表示第一辆车依次访问1号和2号仓库，第二辆车仅访问3号仓库

3. **目标函数设计**：
   - 时间窗惩罚：提前或延迟到达的时间惩罚
   - 路程惩罚：考虑不同车辆每单位距离的成本差异
   - 需求完成惩罚：未满足仓库需求的重度惩罚
   - 车辆使用平衡惩罚：避免车辆使用不均衡

## 实现步骤
1. 解析输入JSON文件
2. 初始化网格图并随机生成边权重
3. 计算所有储供基地到所有仓库的最短路径
4. 使用PSO算法优化车辆调度方案
5. 根据最优方案生成详细调度计划
6. 输出JSON调度方案和PNG路径可视化图

## 可视化要求
- 绘制n×n网格图
- 使用不同颜色标记储供基地、仓库和路径节点
- 用不同颜色的线条表示不同车辆的行驶路径
- 添加图例说明各元素含义
- 保存为PNG格式

## 主要函数结构
1. `main` - 主函数，调用其他函数并处理输入输出
2. `parseInput` - 解析JSON输入文件
3. `generateGrid` - 生成网格图和随机边权重
4. `shortestPath` - 计算最短路径
5. `psoScheduling` - 粒子群算法优化调度
6. `fitnessFunction` - 评估调度方案的适应度
7. `generateDetailedSchedule` - 生成详细调度计划
8. `visualizePaths` - 可视化路径规划
9. `writeOutput` - 输出JSON结果

## 注意事项
- 物资装载量小于仓库需求，车辆需往返于储供基地和仓库之间
- 考虑时间窗约束，车辆需在指定时间窗内到达仓库
- 不同车辆可能仅能装载特定类型的物资
- 优化目标为综合考虑时间、距离、需求完成度和车辆平衡性的总成本